import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';
import 'package:selfeng/features/diagnostic_test/domain/providers/diagnostic_test_provider.dart';
import 'package:selfeng/features/diagnostic_test/domain/repositories/diagnostic_test_repository.dart';
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/state/timer_state.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';

import 'state/diagnostic_test_state.dart';

part 'diagnostic_test_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our diagnostic test state
@riverpod
class DiagnosticTestController extends _$DiagnosticTestController {
  late DiagnosticTestRepository diagnosticTestRepository;
  late TimerController viewTimerModel;
  late ErrorHandler errorHandler;

  @override
  FutureOr<DiagnosticTestState> build() {
    diagnosticTestRepository = ref.watch(diagnosticTestRepositoryProvider);
    viewTimerModel = ref.watch(timerControllerProvider.notifier);
    errorHandler = ErrorHandler(ref.read(crashlyticsServiceProvider));

    // Set custom keys for better error context
    ref.read(crashlyticsServiceProvider).setCustomKeys({
      'feature': 'diagnostic_test',
      'controller': 'DiagnosticTestController',
    });

    return init();
  }

  FutureOr<DiagnosticTestState> init() async {
    List<QuestionModel> respond = [];
    state = const AsyncLoading();

    try {
      final result = await errorHandler.handleAsync(
        () => diagnosticTestRepository.getListDiagnosticTest(),
        context: 'Loading diagnostic test questions',
        metadata: {
          'operation': 'getListDiagnosticTest',
          'feature': 'diagnostic_test',
        },
        fatal: false,
        shouldRethrow: false,
      );

      if (result != null) {
        result.fold(
          (failure) {
            state = AsyncError(failure.message, StackTrace.current);
            // Report the failure to Crashlytics
            errorHandler.reportNetworkError(
              failure,
              StackTrace.current,
              endpoint: '/diagnostic-test/questions',
              method: 'GET',
            );
          },
          (data) {
            respond = data;
          },
        );
      } else {
        // Handle case where operation failed and returned null
        state = AsyncError(
          'Failed to load diagnostic test questions',
          StackTrace.current,
        );
      }
    } catch (error, stackTrace) {
      state = AsyncError(
        'Unexpected error loading questions: $error',
        stackTrace,
      );
      await errorHandler.reportError(
        error,
        stackTrace,
        context: 'Unexpected error in init()',
        metadata: {'operation': 'init'},
        fatal: false,
      );
    }

    return DiagnosticTestState(
      questions: respond,
      controllerScroll: ScrollController(),
    );
  }

  Future<void> saveAnswer() async {
    state = const AsyncLoading();

    try {
      final answers =
          state.value!.questions
              .map((item) => item.answer)
              .whereType<Choice>()
              .toList();

      final result = await errorHandler.handleAsync(
        () => diagnosticTestRepository.saveAnswer(
          answers,
          state.value!.correctCount,
        ),
        context: 'Saving diagnostic test answers',
        metadata: {
          'operation': 'saveAnswer',
          'feature': 'diagnostic_test',
          'answer_count': answers.length,
          'correct_count': state.value!.correctCount,
        },
        fatal: false,
        shouldRethrow: false,
      );

      if (result != null) {
        result.fold(
          (failure) {
            state = AsyncError(failure.message, StackTrace.current);
            // Report the failure to Crashlytics
            errorHandler.reportNetworkError(
              failure,
              StackTrace.current,
              endpoint: '/diagnostic-test/answers',
              method: 'POST',
              requestData: {
                'answers': answers.map((a) => a.toString()).toList(),
                'correct_count': state.value!.correctCount,
              },
            );
          },
          (data) {
            state = AsyncData(state.value!.copyWith(result: data));
          },
        );
      } else {
        // Handle case where operation failed and returned null
        state = AsyncError('Failed to save answers', StackTrace.current);
      }
    } catch (error, stackTrace) {
      state = AsyncError('Unexpected error saving answers: $error', stackTrace);
      await errorHandler.reportError(
        error,
        stackTrace,
        context: 'Unexpected error in saveAnswer()',
        metadata: {'operation': 'saveAnswer'},
        fatal: false,
      );
    }
  }

  void setTimer() {
    viewTimerModel.setTimerState(TimerConcreteState.start);
    viewTimerModel.startTimer();
  }

  // nextPage({int? pageNumber}) {
  //   if (state.value!.currentPage <= state.value!.questions.length) {
  //     state = AsyncData(state.value!.copyWith(
  //       currentPage: pageNumber ?? state.value!.currentPage + 1,
  //     ));
  //   }
  // }

  Future nextPage(BuildContext context, double width) async {
    try {
      if (state.value == null) {
        throw StateError('State is null when navigating to next page');
      }

      if (state.value!.currentPage >= state.value!.questionLength) {
        // Already at the last page, don't report as error
        return;
      }

      state = AsyncData(
        state.value!.copyWith(currentPage: state.value!.currentPage + 1),
      );

      if (state.value?.controllerScroll != null) {
        await state.value!.controllerScroll!.animateTo(
          (state.value!.currentPage) * (width - 25),
          duration: const Duration(seconds: 1),
          curve: Curves.ease,
        );
      }
    } catch (error, stackTrace) {
      // Report UI error
      errorHandler.reportUIError(
        error,
        stackTrace,
        screenName: 'DiagnosticTestPage',
        widget: 'Navigation',
        uiState: {
          'current_page': state.value?.currentPage,
          'total_questions': state.value?.questionLength,
          'has_scroll_controller': state.value?.controllerScroll != null,
        },
      );
      // Re-throw to let the UI handle it
      rethrow;
    }
  }

  Future backPage(double width) async {
    try {
      if (state.value == null) {
        throw StateError('State is null when navigating to previous page');
      }

      if (state.value!.currentPage == 0) {
        // Already at the first page, don't report as error
        return;
      }

      state = AsyncData(
        state.value!.copyWith(currentPage: state.value!.currentPage - 1),
      );

      if (state.value?.controllerScroll != null) {
        await state.value!.controllerScroll!.animateTo(
          (state.value!.currentPage) * (width - 25),
          duration: const Duration(seconds: 1),
          curve: Curves.ease,
        );
      }
    } catch (error, stackTrace) {
      // Report UI error
      errorHandler.reportUIError(
        error,
        stackTrace,
        screenName: 'DiagnosticTestPage',
        widget: 'Navigation',
        uiState: {
          'current_page': state.value?.currentPage,
          'total_questions': state.value?.questionLength,
          'has_scroll_controller': state.value?.controllerScroll != null,
        },
      );
      // Re-throw to let the UI handle it
      rethrow;
    }
  }

  void expandResult() {
    state = AsyncData(
      state.value!.copyWith(expandedResult: !state.value!.expandedResult),
    );
  }

  void selectAnser({required int index, required Choice value}) {
    try {
      if (state.value == null) {
        throw StateError('State is null when selecting answer');
      }

      if (state.value!.currentPage > state.value!.questions.length) {
        throw RangeError('Current page exceeds questions length');
      }

      if (index < 0 || index >= state.value!.questions.length) {
        throw RangeError('Question index out of bounds: $index');
      }

      List<QuestionModel> tempData = List.from(state.value!.questions);
      QuestionModel temp = tempData[index];
      tempData[index] = temp.copyWith(
        state: QuestionConcreteState.answered,
        answer: value,
        isCorrect: value.isCorrect,
      );
      state = AsyncData(state.value!.copyWith(questions: tempData));
    } catch (error, stackTrace) {
      // Report business logic error
      errorHandler.reportBusinessLogicError(
        error,
        stackTrace,
        feature: 'diagnostic_test',
        operation: 'selectAnswer',
        businessContext: {
          'question_index': index,
          'selected_choice': value.toString(),
          'current_page': state.value?.currentPage,
          'total_questions': state.value?.questions.length,
        },
      );
      // Re-throw to let the UI handle it
      rethrow;
    }
  }
}

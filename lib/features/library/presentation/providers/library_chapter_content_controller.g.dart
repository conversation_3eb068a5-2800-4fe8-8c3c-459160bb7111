// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_chapter_content_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(LibraryChapterContentController)
const libraryChapterContentControllerProvider =
    LibraryChapterContentControllerFamily._();

final class LibraryChapterContentControllerProvider
    extends
        $AsyncNotifierProvider<
          LibraryChapterContentController,
          LibraryChapterContentState
        > {
  const LibraryChapterContentControllerProvider._({
    required LibraryChapterContentControllerFamily super.from,
    required (String, String) super.argument,
  }) : super(
         retry: null,
         name: r'libraryChapterContentControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$libraryChapterContentControllerHash();

  @override
  String toString() {
    return r'libraryChapterContentControllerProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  LibraryChapterContentController create() => LibraryChapterContentController();

  @override
  bool operator ==(Object other) {
    return other is LibraryChapterContentControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$libraryChapterContentControllerHash() =>
    r'24c5dbeb80657ab80e42c1d22b2448878c7b636e';

final class LibraryChapterContentControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          LibraryChapterContentController,
          AsyncValue<LibraryChapterContentState>,
          LibraryChapterContentState,
          FutureOr<LibraryChapterContentState>,
          (String, String)
        > {
  const LibraryChapterContentControllerFamily._()
    : super(
        retry: null,
        name: r'libraryChapterContentControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  LibraryChapterContentControllerProvider call(String level, String chapter) =>
      LibraryChapterContentControllerProvider._(
        argument: (level, chapter),
        from: this,
      );

  @override
  String toString() => r'libraryChapterContentControllerProvider';
}

abstract class _$LibraryChapterContentController
    extends $AsyncNotifier<LibraryChapterContentState> {
  late final _$args = ref.$arg as (String, String);
  String get level => _$args.$1;
  String get chapter => _$args.$2;

  FutureOr<LibraryChapterContentState> build(String level, String chapter);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2);
    final ref =
        this.ref
            as $Ref<
              AsyncValue<LibraryChapterContentState>,
              LibraryChapterContentState
            >;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<LibraryChapterContentState>,
                LibraryChapterContentState
              >,
              AsyncValue<LibraryChapterContentState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

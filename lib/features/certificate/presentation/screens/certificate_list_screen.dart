import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/certificate/presentation/providers/certificate_list_controller.dart';
import 'package:selfeng/features/setting/presentation/widgets/congratulatory_widget.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/features/certificate/presentation/widgets/certificate_level_section.dart';
import 'package:selfeng/shared/theme/app_colors.dart';
import 'package:selfeng/shared/theme/text_styles.dart';

class CertificateListScreen extends ConsumerWidget {
  const CertificateListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final certificateListState = ref.watch(certificateListControllerProvider);

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.black.withValues(alpha: 0.7),
          ),
          onPressed: () => context.pop(),
        ),
        title: Text(
          context.loc.certificate_list,
          style: AppTextStyles.titleLg.copyWith(color: AppColors.black),
        ),
        backgroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: certificateListState.when(
        loading:
            () => Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            ),
        error:
            (error, stackTrace) =>
                Center(child: Text(context.loc.failed_to_load_certificates)),
        data: (state) {
          if (state.groupedCertificates.isEmpty) {
            return LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: constraints.maxHeight,
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 180,
                            child: Lottie.asset(
                              'assets/animations/smallc.json',
                              repeat: false,
                              fit: BoxFit.contain,
                            ),
                          ),
                          const SizedBox(height: 32),
                          Text(
                            context.loc.no_certificates_yet,
                            textAlign: TextAlign.center,
                            style: AppTextStyles.titleLg.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 20),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(
                                  alpha: 0.05,
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.2,
                                  ),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                context.loc.every_expert_was_beginner,
                                textAlign: TextAlign.center,
                                style: AppTextStyles.bodyLg.copyWith(
                                  color: AppColors.black,
                                  height: 1.5,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 14,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.extraLightGrey.withValues(
                                  alpha: 0.5,
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.lightGrey.withValues(
                                    alpha: 0.5,
                                  ),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.trending_up,
                                    color: AppColors.primary,
                                    size: 18,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      context.loc.complete_lessons_practice,
                                      textAlign: TextAlign.center,
                                      style: AppTextStyles.body.copyWith(
                                        color: AppColors.black.withValues(
                                          alpha: 0.8,
                                        ),
                                        height: 1.4,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 32),
                          Container(
                            width: 280,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 12,
                                  offset: const Offset(0, 6),
                                ),
                              ],
                            ),
                            child: ElevatedButton.icon(
                              onPressed:
                                  () => context.goNamed(
                                    RouterName.dashboardScreen,
                                  ),
                              icon: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: AppColors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.rocket_launch,
                                  color: AppColors.white,
                                  size: 20,
                                ),
                              ),
                              label: Text(
                                context.loc.start_learning_now,
                                style: AppTextStyles.bodyLg.copyWith(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 18,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          }

          final levels = state.groupedCertificates.keys.toList();

          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: levels.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return const Padding(
                  padding: EdgeInsets.only(bottom: 16.0),
                  child: CongratulatoryWidget(),
                );
              }
              final level = levels[index - 1];
              final certificates = state.groupedCertificates[level]!;

              return CertificateLevelSection(
                level: level,
                certificates: certificates,
              );
            },
          );
        },
      ),
    );
  }
}

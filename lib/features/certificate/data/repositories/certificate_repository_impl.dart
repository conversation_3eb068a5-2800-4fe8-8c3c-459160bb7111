import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

import '../../domain/models/certificate.dart';
import '../../domain/repositories/certificate_repository.dart';
import '../models/certificate_model.dart';
import '../models/certificate_level_model.dart';

class CertificateRepositoryImpl implements CertificateRepository {
  final FirestoreServiceRepository _firestoreService;

  CertificateRepositoryImpl(this._firestoreService);

  @override
  Future<Either<AppException, List<Certificate>>> getCertificates() async {
    try {
      final user = _firestoreService.firebaseAuth.currentUser;
      if (user == null) {
        return Left(
          AppException(
            identifier: 'getCertificates',
            statusCode: 401,
            message: 'User not authenticated',
          ),
        );
      }

      final query =
          await _firestoreService.fireStore
              .collection('user-data')
              .doc(user.uid)
              .collection('certificates')
              .orderBy('generatedAt', descending: true)
              .get();

      // Map Firestore docs to domain entities
      final certificates =
          query.docs.map((doc) {
            return _mapFirestoreDocToCertificate(doc);
          }).toList();

      return Right(certificates);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'getCertificates',
          statusCode: 0,
          message: 'Failed to fetch certificates: ${e.toString()}',
        ),
      );
    }
  }

  Certificate _mapFirestoreDocToCertificate(
    QueryDocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data();

    // Extract Firestore structure fields
    final String? pageOne = data['certificateUrlPageOne'] as String?;
    final String? pageTwo = data['certificateUrlPageTwo'] as String?;
    final String levelStr = (data['level'] as String?) ?? '';

    // Handle generatedAt as ISO string or Firestore Timestamp
    DateTime issuedAt = DateTime.fromMillisecondsSinceEpoch(0);
    final rawGeneratedAt = data['generatedAt'];
    if (rawGeneratedAt is String) {
      try {
        issuedAt = DateTime.parse(rawGeneratedAt);
      } catch (_) {
        // If parsing fails, keep default value
      }
    } else if (rawGeneratedAt is Timestamp) {
      issuedAt = rawGeneratedAt.toDate();
    }

    // Extract optional nested fields
    final Map<String, dynamic>? scoresData =
        data['scores'] as Map<String, dynamic>?;
    final Map<String, dynamic>? predicatesData =
        data['predicates'] as Map<String, dynamic>?;

    // Create scores object if data exists
    CertificateScores? scores;
    if (scoresData != null) {
      scores = CertificateScoresModel.fromJson(scoresData);
    }

    // Create predicates object if data exists
    CertificatePredicates? predicates;
    if (predicatesData != null) {
      predicates = CertificatePredicatesModel.fromJson(predicatesData);
    }

    // Create level model
    final levelModel = CertificateLevelModel(
      id: levelStr,
      name: levelStr.toUpperCase(),
      description: '',
    );

    // Build title and description for UI compatibility
    final title = 'Level $levelStr Certificate';
    final description = _buildDescription(scores, predicates);

    return CertificateModel(
      id: doc.id,
      title: title,
      description: description,
      dateIssued: issuedAt,
      certificateUrl: pageOne ?? '',
      certificateUrlPage2: pageTwo,
      level: levelModel,
      scores: scores,
      predicates: predicates,
    );
  }

  String _buildDescription(
    CertificateScores? scores,
    CertificatePredicates? predicates,
  ) {
    final parts = <String>[];

    if (scores != null) {
      final scoresStr = [
        if (scores.pronunciation != null)
          'Pronunciation: ${scores.pronunciation}',
        if (scores.listening != null) 'Listening: ${scores.listening}',
        if (scores.speaking != null) 'Speaking: ${scores.speaking}',
      ].join(' · ');
      if (scoresStr.isNotEmpty) parts.add(scoresStr);
    }

    if (predicates != null) {
      final predsStr = [
        if (predicates.pronunciation != null)
          'Pronunciation: ${predicates.pronunciation}',
        if (predicates.listening != null) 'Listening: ${predicates.listening}',
        if (predicates.speaking != null) 'Speaking: ${predicates.speaking}',
      ].join(' · ');
      if (predsStr.isNotEmpty) parts.add(predsStr);
    }

    return parts.isEmpty ? '' : parts.join(' | ');
  }
}

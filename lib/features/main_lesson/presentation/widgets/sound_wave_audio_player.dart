import 'dart:async';
import 'dart:math' as math;
import 'package:audioplayers/audioplayers.dart' as ap;
import 'package:flutter/material.dart';
import 'package:selfeng/services/audio/audio_player_service.dart';

class SoundWaveAudioPlayer extends StatefulWidget {
  final String audioUrl;
  final bool isEnabled;
  final void Function()? onComplete;
  final AudioPlayerService? audioPlayerService;

  const SoundWaveAudioPlayer({
    super.key,
    required this.audioUrl,
    this.isEnabled = true,
    this.onComplete,
    this.audioPlayerService,
  });

  @override
  State<SoundWaveAudioPlayer> createState() => _SoundWaveAudioPlayerState();
}

class _SoundWaveAudioPlayerState extends State<SoundWaveAudioPlayer>
    with TickerProviderStateMixin {
  late AnimationController _waveAnimationController;
  late AnimationController _playButtonController;

  late AudioPlayerService _audioPlayerService;
  ap.PlayerState _playerState = ap.PlayerState.stopped;
  Duration? _duration;
  Duration _position = Duration.zero;
  List<double> _waveformData = [];
  bool _isWaveformReady = false;
  bool _isInteracting = false;
  bool _isLoading = false;
  double? _hoverPosition;

  StreamSubscription<ap.PlayerState>? _playerStateSubscription;
  StreamSubscription<void>? _playerCompleteSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;

  String? _currentAudioUrl;

  @override
  void didUpdateWidget(covariant SoundWaveAudioPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.audioUrl != widget.audioUrl) {
      _currentAudioUrl = widget.audioUrl;
      _handleAudioUrlChange();
    }
  }

  Future<void> _handleAudioUrlChange() async {
    // Stop current playback if playing
    if (_playerState == ap.PlayerState.playing ||
        _playerState == ap.PlayerState.paused) {
      await _audioPlayerService.stop();
    }
    // Reset state
    if (mounted) {
      setState(() {
        _playerState = ap.PlayerState.stopped;
        _position = Duration.zero;
        _duration = null;
        _isLoading = false;
      });
    }
    // Regenerate waveform for new URL
    _extractWaveformData();
  }

  @override
  void initState() {
    super.initState();
    _audioPlayerService = widget.audioPlayerService ?? RealAudioPlayerService();
    _currentAudioUrl = widget.audioUrl;

    // Animation controllers
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _initializeAudioPlayer();
    _extractWaveformData();
  }

  Future<void> _extractWaveformData() async {
    try {
      // For remote URLs, we'll use a different approach
      // The audio_waveforms package doesn't handle remote URLs well
      // So we'll generate a more realistic waveform based on audio analysis
      // or use the audioplayers package to get some audio information

      // For now, we'll use a more sophisticated generated waveform
      // that varies based on the URL to make it consistent per audio file
      _generateConsistentWaveform();
    } catch (e) {
      // Fallback to generated waveform
      setState(() {
        _isWaveformReady = false;
      });
    }
  }

  void _generateConsistentWaveform() {
    // Generate a consistent waveform based on the audio URL
    // This ensures the same audio file always shows the same waveform
    final urlHash = widget.audioUrl.hashCode;
    final random = math.Random(urlHash); // Seeded random for consistency

    final List<double> waveform = [];
    for (int i = 0; i < 100; i++) {
      // Create a more realistic waveform pattern
      final double baseAmplitude = 0.1 + random.nextDouble() * 0.8;
      final double frequency1 = math.sin(i * 0.1) * 0.3;
      final double frequency2 = math.sin(i * 0.05) * 0.2;
      final double noise = (random.nextDouble() - 0.5) * 0.1;

      final double amplitude = (baseAmplitude + frequency1 + frequency2 + noise)
          .clamp(0.0, 1.0);
      waveform.add(amplitude);
    }

    setState(() {
      _waveformData = waveform;
      _isWaveformReady = true;
    });
  }

  void _initializeAudioPlayer() {
    // Listen to player state changes
    _playerStateSubscription = _audioPlayerService.onPlayerStateChanged.listen((
      state,
    ) {
      if (mounted) {
        setState(() {
          _playerState = state;
        });

        if (state == ap.PlayerState.playing) {
          _waveAnimationController.repeat();
          _playButtonController.forward();
        } else {
          _waveAnimationController.stop();
          _playButtonController.reverse();
        }
      }
    });

    _playerCompleteSubscription = _audioPlayerService.onPlayerComplete.listen((
      _,
    ) async {
      // Note: `stop()` might release the audio source.
      // Re-initializing might be needed if playing again.
      await _audioPlayerService.stop();
      if (mounted) {
        setState(() {
          _playerState = ap.PlayerState.stopped;
          _position = Duration.zero; // Reset position on complete
        });
      }
      widget.onComplete?.call();
    });

    // Listen to duration changes
    _durationSubscription = _audioPlayerService.onDurationChanged.listen((
      duration,
    ) {
      if (mounted) {
        setState(() {
          _duration = duration;
        });
      }
    });

    // Listen to position changes
    _positionSubscription = _audioPlayerService.onPositionChanged.listen((
      position,
    ) {
      if (mounted) {
        setState(() {
          _position = position;
        });
      }
    });
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _playerCompleteSubscription?.cancel();
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _audioPlayerService.dispose();
    _waveAnimationController.dispose();
    _playButtonController.dispose();
    super.dispose();
  }

  Future<void> _togglePlayPause() async {
    if (!widget.isEnabled) return;

    // Update current URL if not set
    if (_currentAudioUrl != widget.audioUrl) {
      _currentAudioUrl = widget.audioUrl;
      await _handleAudioUrlChange();
    }

    try {
      if (_playerState == ap.PlayerState.playing) {
        await _audioPlayerService.pause();
      } else {
        if (_playerState == ap.PlayerState.stopped ||
            _playerState == ap.PlayerState.completed) {
          if (mounted) {
            setState(() {
              _isLoading = true;
            });
          }
          await _audioPlayerService
              .play(ap.UrlSource(widget.audioUrl))
              .then((_) {
                if (mounted) {
                  _isLoading = false;
                }
              })
              .catchError((_) {
                if (mounted) {
                  _isLoading = false;
                }
              });
        } else {
          await _audioPlayerService.resume();
        }
      }
    } catch (e) {
      // Handle exception silently
    }
  }

  Future<void> _seekToPosition(double progress) async {
    if (!widget.isEnabled || _duration == null) return;

    try {
      final seekPosition = Duration(
        milliseconds: (progress * _duration!.inMilliseconds).round(),
      );
      await _audioPlayerService.seek(seekPosition);
    } catch (e) {
      // Handle exception silently
    }
  }

  void _handleWaveformTap(TapDownDetails details, Size size) {
    if (!widget.isEnabled || _duration == null) return;

    setState(() {
      _isInteracting = true;
    });

    final double progress = (details.localPosition.dx / size.width).clamp(
      0.0,
      1.0,
    );
    _seekToPosition(progress);

    // Reset interaction state after a short delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _isInteracting = false;
        });
      }
    });
  }

  void _handleWaveformPanStart(DragStartDetails details) {
    if (!widget.isEnabled || _duration == null) return;

    setState(() {
      _isInteracting = true;
    });
  }

  void _handleWaveformPan(DragUpdateDetails details, Size size) {
    if (!widget.isEnabled || _duration == null) return;

    final double progress = (details.localPosition.dx / size.width).clamp(
      0.0,
      1.0,
    );
    _seekToPosition(progress);
  }

  void _handleWaveformPanEnd(DragEndDetails details) {
    if (!widget.isEnabled || _duration == null) return;

    setState(() {
      _isInteracting = false;
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 60,
      child: Row(
        children: [
          // Play/Pause Button
          GestureDetector(
            onTap: _isLoading ? null : _togglePlayPause,
            child: AnimatedBuilder(
              animation: _playButtonController,
              builder: (context, child) {
                return Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xffE21F29),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xffE21F29).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    _isLoading
                        ? Icons.circle_rounded
                        : _playerState == ap.PlayerState.playing
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.white,
                    size: 24,
                  ),
                );
              },
            ),
          ),

          const SizedBox(width: 12),

          // Sound Wave Visualization
          Expanded(
            child: SizedBox(
              height: 40,
              child: AnimatedBuilder(
                animation: _waveAnimationController,
                builder: (context, child) {
                  return LayoutBuilder(
                    builder: (context, constraints) {
                      return GestureDetector(
                        onTapDown:
                            (details) => _handleWaveformTap(
                              details,
                              constraints.biggest,
                            ),
                        onPanStart: _handleWaveformPanStart,
                        onPanUpdate:
                            (details) => _handleWaveformPan(
                              details,
                              constraints.biggest,
                            ),
                        onPanEnd: _handleWaveformPanEnd,
                        child: MouseRegion(
                          cursor:
                              widget.isEnabled && _duration != null
                                  ? SystemMouseCursors.click
                                  : SystemMouseCursors.basic,
                          onHover: (event) {
                            if (widget.isEnabled && _duration != null) {
                              setState(() {
                                _hoverPosition = (event.localPosition.dx /
                                        constraints.biggest.width)
                                    .clamp(0.0, 1.0);
                              });
                            }
                          },
                          onExit: (event) {
                            setState(() {
                              _hoverPosition = null;
                            });
                          },
                          child: CustomPaint(
                            painter: SoundWavePainter(
                              animation: _waveAnimationController,
                              isPlaying: _playerState == ap.PlayerState.playing,
                              progress:
                                  _duration != null &&
                                          _duration!.inMilliseconds > 0
                                      ? _position.inMilliseconds /
                                          _duration!.inMilliseconds
                                      : 0.0,
                              waveformData:
                                  _isWaveformReady ? _waveformData : null,
                              isInteracting: _isInteracting,
                              hoverPosition: _hoverPosition,
                            ),
                            size: Size.infinite,
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),

          // Time Display
          Text(
            _duration != null
                ? _formatDuration(_duration! - _position)
                : _formatDuration(Duration.zero),
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xffE21F29),
            ),
          ),
        ],
      ),
    );
  }
}

/// Real implementation of AudioPlayerService using audioplayers.
class RealAudioPlayerService implements AudioPlayerService {
  late final ap.AudioPlayer _audioPlayer;

  RealAudioPlayerService() {
    _audioPlayer = ap.AudioPlayer();
  }

  @override
  Stream<ap.PlayerState> get onPlayerStateChanged =>
      _audioPlayer.onPlayerStateChanged;

  @override
  Stream<void> get onPlayerComplete =>
      _audioPlayer.onPlayerComplete.map((_) {});

  @override
  Stream<Duration> get onDurationChanged => _audioPlayer.onDurationChanged;

  @override
  Stream<Duration> get onPositionChanged => _audioPlayer.onPositionChanged;

  @override
  Future<void> play(ap.UrlSource source) => _audioPlayer.play(source);

  @override
  Future<void> pause() => _audioPlayer.pause();

  @override
  Future<void> resume() => _audioPlayer.resume();

  @override
  Future<void> stop() => _audioPlayer.stop();

  @override
  Future<void> seek(Duration position) => _audioPlayer.seek(position);

  @override
  Future<void> dispose() => _audioPlayer.dispose();
}

class SoundWavePainter extends CustomPainter {
  final Animation<double> animation;
  final bool isPlaying;
  final double progress;
  final List<double>? waveformData;
  final bool isInteracting;
  final double? hoverPosition;

  SoundWavePainter({
    required this.animation,
    required this.isPlaying,
    required this.progress,
    this.waveformData,
    this.isInteracting = false,
    this.hoverPosition,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final activePaint =
        Paint()
          ..color = const Color(0xffE21F29)
          ..style = PaintingStyle.fill;

    final inactivePaint =
        Paint()
          ..color = const Color(0xffE21F29).withAlpha(77)
          ..style = PaintingStyle.fill;

    // Enhanced colors when interacting
    final interactingActivePaint =
        Paint()
          ..color = const Color(0xffE21F29).withAlpha(229)
          ..style = PaintingStyle.fill;

    final interactingInactivePaint =
        Paint()
          ..color = const Color(0xffE21F29).withAlpha(128)
          ..style = PaintingStyle.fill;

    const int barCount = 40;
    final double barWidth = size.width / barCount;
    final double centerY = size.height / 2;

    for (int i = 0; i < barCount; i++) {
      final double x = i * barWidth + barWidth / 2;

      // Use real waveform data if available, otherwise generate pattern
      final double baseHeight =
          waveformData != null && waveformData!.isNotEmpty
              ? _getRealWaveformHeight(i, barCount)
              : _getBarHeight(i, barCount);

      final double animatedHeight =
          isPlaying
              ? baseHeight *
                  (0.4 +
                      0.6 *
                          (0.5 +
                              0.5 *
                                  math.sin(
                                    animation.value * 2 * math.pi + i * 0.4,
                                  )))
              : baseHeight * 0.3;

      final double barHeight = math.max(2.0, animatedHeight);

      // Determine if this bar should be active based on progress
      final bool isActive = (i / barCount) <= progress;

      // Choose paint based on interaction state
      final Paint activeBarPaint =
          isInteracting ? interactingActivePaint : activePaint;
      final Paint inactiveBarPaint =
          isInteracting ? interactingInactivePaint : inactivePaint;

      final rect = RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(x, centerY),
          width: barWidth * 0.6,
          height: barHeight,
        ),
        const Radius.circular(1),
      );

      canvas.drawRRect(rect, isActive ? activeBarPaint : inactiveBarPaint);
    }

    // Draw hover indicator line
    if (hoverPosition != null && !isInteracting) {
      final hoverPaint =
          Paint()
            ..color = const Color(0xffE21F29).withAlpha(153)
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke;

      final double hoverX = hoverPosition! * size.width;
      canvas.drawLine(
        Offset(hoverX, 0),
        Offset(hoverX, size.height),
        hoverPaint,
      );
    }
  }

  double _getRealWaveformHeight(int index, int totalBars) {
    if (waveformData == null || waveformData!.isEmpty) {
      return _getBarHeight(index, totalBars);
    }

    // Map the bar index to the waveform data index
    final int dataIndex = (index * waveformData!.length / totalBars).floor();
    final int clampedIndex = dataIndex.clamp(0, waveformData!.length - 1);

    // Get the amplitude value and normalize it
    final double amplitude = waveformData![clampedIndex].abs();

    // Scale the amplitude to a reasonable bar height (3-25 pixels)
    final double scaledHeight = 3.0 + (amplitude * 22.0);

    return math.max(3.0, scaledHeight);
  }

  double _getBarHeight(int index, int totalBars) {
    // Create a more realistic audio waveform pattern
    final double normalizedIndex = index / totalBars;

    // Create multiple frequency components for a more natural look
    final double wave1 = math.sin(normalizedIndex * math.pi * 3) * 0.4;
    final double wave2 = math.sin(normalizedIndex * math.pi * 7) * 0.25;
    final double wave3 = math.sin(normalizedIndex * math.pi * 13) * 0.15;
    final double wave4 = math.sin(normalizedIndex * math.pi * 19) * 0.1;

    // Add some randomness for more natural variation
    final double randomFactor =
        (index * 17) % 100 / 100.0; // Pseudo-random based on index
    final double randomVariation = (randomFactor - 0.5) * 0.2;

    // Combine waves with different amplitudes for realistic audio visualization
    final double combinedWave = wave1 + wave2 + wave3 + wave4 + randomVariation;

    // Ensure minimum height and scale appropriately
    return math.max(3.0, 12 + combinedWave * 15);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

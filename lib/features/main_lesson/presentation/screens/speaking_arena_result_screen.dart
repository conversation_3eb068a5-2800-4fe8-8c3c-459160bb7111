import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/incomplete_challenge.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/score_progress_indicator.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/screenshot_share_button.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';
import 'package:screenshot/screenshot.dart';

class SpeakingArenaResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  final String stage;

  const SpeakingArenaResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  });

  @override
  ConsumerState<SpeakingArenaResultScreen> createState() =>
      _SpeakingArenaResultScreenState();
}

class _SpeakingArenaResultScreenState
    extends ConsumerState<SpeakingArenaResultScreen>
    with TickerProviderStateMixin {
  late final AudioPlayer _bgmPlayer = AudioPlayer();
  // Create a screenshot controller
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongratsSound() async {
    final isAudioEnabled = ref.read(audioToggleProvider);
    if (isAudioEnabled) {
      await _bgmPlayer.stop();
      try {
        await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
      } catch (e) {
        // Handle exception silently
      }
    }
  }

  void _navigateToNext() {
    int currentChapter = int.parse(widget.chapter.trim(), radix: 10);
    bool isLastChapter =
        widget.level == 'C2' && currentChapter == 7 || currentChapter % 8 == 0;

    if (isLastChapter) {
      _navigateToCertificateScreen();
    } else {
      _navigateToNextChapter();
    }
  }

  void _navigateToNextChapter() {
    customNav(
      context,
      RouterName.chapterTitle,
      params: {
        'level': widget.level,
        'chapter': (int.parse(widget.chapter.trim(), radix: 10) + 1).toString(),
      },
      isReplace: true,
    );
  }

  void _navigateToCertificateScreen() {
    customNav(
      context,
      RouterName.certificateNotification,
      params: {'level': widget.level},
      isReplace: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.values.byName(widget.stage),
    );

    final asyncState = ref.watch(provider);
    final viewModel = ref.read(provider.notifier);

    return Screenshot(
      controller: _screenshotController,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: AsyncValueX(asyncState).when(
          // Loading State: Show a centered progress indicator
          loading:
              () => const Center(child: CircularProgressIndicator.adaptive()),
          error:
              (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    error.toString(),
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ),
          data: (state) => _buildSuccessContent(context, state, viewModel),
        ),
      ),
    );
  }

  // Widget _buildSuccessContentWithLoadingCheck(
  //   BuildContext context,
  //   SpeakingState state,
  //   SpeakingController viewModel,
  // ) {
  //   try {
  //     // Show loading indicator if state is still loading
  //     if (state.isLoading) {
  //       return const Center(child: CircularProgressIndicator.adaptive());
  //     }

  //     if (widget.stage == 'stage3') {
  //       // Use centralized validation from the controller
  //       final resultStage2 = state.resultStage2;
  //       final resultStage3 = state.resultStage3;

  //       // Handle null results with crash reporting
  //       if (resultStage2 == null || resultStage3 == null) {
  //         _reportCompletionError(
  //           'Missing result data for speaking arena stages',
  //           context: {
  //             'stage2_null': resultStage2 == null,
  //             'stage3_null': resultStage3 == null,
  //             'required_paths_count': viewModel.requiredContentPaths.length,
  //             'level': widget.level,
  //             'chapter': widget.chapter,
  //           },
  //         );
  //         return const Center(child: CircularProgressIndicator.adaptive());
  //       }

  //       // Use centralized completion detection from controller
  //       final isStage2Complete = viewModel.isStageComplete(
  //         SpeakingStage.stage2,
  //       );
  //       final isStage3Complete = viewModel.isStageComplete(
  //         SpeakingStage.stage3,
  //       );

  //       if (!isStage2Complete || !isStage3Complete) {
  //         final stage2MissingPaths = viewModel.getMissingPathsForStage(
  //           SpeakingStage.stage2,
  //         );
  //         final stage3MissingPaths = viewModel.getMissingPathsForStage(
  //           SpeakingStage.stage3,
  //         );

  //         _reportCompletionError(
  //           'Incomplete path completion detected using centralized validation',
  //           context: {
  //             'stage2_complete': isStage2Complete,
  //             'stage3_complete': isStage3Complete,
  //             'stage2_missing_count': stage2MissingPaths.length,
  //             'stage3_missing_count': stage3MissingPaths.length,
  //             'stage2_missing_paths': stage2MissingPaths.toList(),
  //             'stage3_missing_paths': stage3MissingPaths.toList(),
  //             'required_paths_count': viewModel.requiredContentPaths.length,
  //             'level': widget.level,
  //             'chapter': widget.chapter,
  //           },
  //         );
  //         return _buildErrorContent(context, 'Incomplete lesson progress');
  //       }

  //       // Validate score data consistency using centralized method
  //       final isStage2ScoreValid = viewModel.isScoreDataValid(
  //         resultStage2,
  //         SpeakingStage.stage2,
  //       );
  //       final isStage3ScoreValid = viewModel.isScoreDataValid(
  //         resultStage3,
  //         SpeakingStage.stage3,
  //       );

  //       if (!isStage2ScoreValid || !isStage3ScoreValid) {
  //         _reportCompletionError(
  //           'Score data validation failed using centralized validation',
  //           context: {
  //             'stage2_score_valid': isStage2ScoreValid,
  //             'stage3_score_valid': isStage3ScoreValid,
  //             'required_paths_count': viewModel.requiredContentPaths.length,
  //             'stage2_data_count': resultStage2.dataCount,
  //             'stage3_data_count': resultStage3.dataCount,
  //             'level': widget.level,
  //             'chapter': widget.chapter,
  //           },
  //         );
  //         return _buildErrorContent(
  //           context,
  //           'Score data inconsistency detected',
  //         );
  //       }
  //     }

  //     return _buildSuccessContent(context, state, viewModel);
  //   } catch (error, stackTrace) {
  //     // Catch any unexpected errors and report them
  //     _reportCompletionError(
  //       'Unexpected error in success content loading check',
  //       error: error,
  //       stackTrace: stackTrace,
  //       context: {
  //         'stage': widget.stage,
  //         'level': widget.level,
  //         'chapter': widget.chapter,
  //       },
  //     );
  //     return _buildErrorContent(context, 'An unexpected error occurred');
  //   }
  // }

  Widget _buildSuccessContent(
    BuildContext context,
    SpeakingState state,
    SpeakingController viewModel,
  ) {
    try {
      final resultStage2 = state.resultStage2;
      final resultStage3 = state.resultStage3;

      // Show loading indicator while data is still being processed
      if (resultStage2 == null || resultStage3 == null) {
        return const Center(child: CircularProgressIndicator.adaptive());
      }

      // Use centralized completion detection - show success only when both main stages are complete
      final bool areBothStagesComplete = viewModel.areBothMainStagesComplete();

      if (areBothStagesComplete) {
        final stage2Score = resultStage2;
        final stage3Score = resultStage3;

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _playCongratsSound();
        });

        return DefaultTabController(
          length: 2,
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 82),
                    _buildEvaluationHeader(context),
                    const SizedBox(height: 24),
                    _buildTabBar(context),
                    SizedBox(
                      // Give TabBarView a reasonable height so it can render
                      height: MediaQuery.of(context).size.height * 0.75,
                      child: _buildTabBarView(
                        context,
                        stage2Score,
                        stage3Score,
                      ),
                    ),
                    if (MediaQuery.of(context).size.width <= 360) _nextButton(),
                  ],
                ),
              ),
              if (MediaQuery.of(context).size.width > 360) _nextButton(),
            ],
          ),
        );
      } else {
        return const IncompleteChallenge(
          assetImagePath: 'assets/images/main_lesson/notcomplete2.png',
        );
      }
    } catch (error, stackTrace) {
      // Handle any unexpected errors in success content building
      _reportCompletionError(
        'Unexpected error in success content building',
        error: error,
        stackTrace: stackTrace,
        context: {
          'stage': widget.stage,
          'level': widget.level,
          'chapter': widget.chapter,
        },
      );
      return _buildErrorContent(context, 'An unexpected error occurred');
    }
  }

  Widget _nextButton() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            VButtonGradient(
              title: context.loc.next,
              onTap: () => _navigateToNext(),
              isBorder: false,
            ),
            SizedBox(height: 16),
            ScreenshotShareButton(
              title: 'Chapter ${widget.chapter} Speaking Arena',
              screenshotController: _screenshotController,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorContent(BuildContext context, String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Error Loading Results',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 16),
            TextButton(
              onPressed: () {
                // Allow user to retry or navigate back
                Navigator.of(context).pop();
              },
              child: Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  /// Report UI errors to Firebase Crashlytics following project guidelines
  ///
  /// Uses the recommended UI error reporting pattern from the crashlytics service
  /// with appropriate categorization and context for speaking arena result screen errors.
  Future<void> _reportCompletionError(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    try {
      final crashlytics = ref.read(crashlyticsServiceProvider);

      // Set screen-specific context
      await crashlytics.setCustomKeys({
        'current_screen': 'speaking_arena_result_screen',
        'feature': 'speaking_arena',
        'operation': 'completion_validation',
      });

      // Report as UI error with flattened context (Firebase Crashlytics requirement)
      await crashlytics.recordError(
        error ?? Exception(message),
        stackTrace ?? StackTrace.current,
        reason: 'Speaking Arena Result Screen Error',
        context: {
          'category': 'ui_error',
          'screen_name': 'SpeakingArenaResultScreen',
          'widget': 'completion_validation',
          // Flatten UI state to meet Firebase Crashlytics type requirements
          'ui_stage': widget.stage,
          'ui_level': widget.level,
          'ui_chapter': widget.chapter,
          'ui_path': widget.path,
          'error_type': error?.runtimeType.toString() ?? 'ValidationError',
          'timestamp': DateTime.now().toIso8601String(),
          // Flatten any additional context to ensure type safety
          if (context != null)
            ...context.map((key, value) => MapEntry(key, value.toString())),
        },
        fatal: false,
      );

      // Log breadcrumb for debugging context
      crashlytics.log('Speaking Arena Result Error: $message');
    } catch (e) {
      // Don't let error reporting errors break the app - follow safe error reporting principle
      debugPrint('Failed to report completion error: $e');
    }
  }

  Widget _buildEvaluationHeader(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ), // Adjusted padding
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        '${context.loc.chapter} ${widget.chapter}',
        style: Theme.of(
          context,
        ).textTheme.headlineSmall!.copyWith(color: Color(0xff680007)),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return TabBar(
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: Colors.transparent,
      labelStyle: Theme.of(context).textTheme.titleMedium,
      unselectedLabelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .6),
      ),
      tabs: <Widget>[
        Tab(text: '${context.loc.stage} II'),
        Tab(text: '${context.loc.stage} III'),
      ],
    );
  }

  Widget _buildTabBarView(
    BuildContext context,
    SpeakingAgregateScore resultStage2,
    SpeakingAgregateScore resultStage3,
  ) {
    return TabBarView(
      children: <Widget>[
        _SpeakingResultDetailsView(score: resultStage2),
        _SpeakingResultDetailsView(score: resultStage3),
      ],
    );
  }
}

class _SpeakingResultDetailsView extends StatelessWidget {
  final SpeakingAgregateScore score;

  const _SpeakingResultDetailsView({required this.score});

  int _calculateScore(double scoreValue, int dataCount) {
    if (dataCount == 0 || scoreValue.isNaN || scoreValue.isInfinite) {
      return 0;
    }
    return (scoreValue / dataCount).round();
  }

  int _calculateAverage(List<int> scoreValues) {
    if (scoreValues.isEmpty) {
      return 0;
    }
    final total = scoreValues.fold(0, (sum, value) => sum + value);
    return (total / scoreValues.length).round();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate individual scores for clarity
    final accuracyScore = _calculateScore(score.accuracyScore, score.dataCount);
    final fluencyScore = _calculateScore(score.fluencyScore, score.dataCount);
    final rhythmScore = _calculateScore(
      score.prosodyScore,
      score.dataCount,
    ); // Renamed for clarity

    final averageScore = _calculateAverage([
      accuracyScore,
      fluencyScore,
      rhythmScore,
    ]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        TweenAnimationBuilder<int>(
          tween: IntTween(begin: 0, end: averageScore),
          duration: const Duration(milliseconds: 1800),
          builder: (context, value, child) {
            return Text(
              '$value',
              style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                fontSize: 70,
                color:
                    value > 90
                        ? const Color(0xff36AA34)
                        : value > 70
                        ? const Color(0xffF5BE48)
                        : const Color(0xff93000F),
              ),
            );
          },
        ),
        SizedBox(height: 8),
        TweenAnimationBuilder<int>(
          tween: IntTween(begin: 0, end: averageScore),
          duration: const Duration(milliseconds: 1800),
          builder: (context, value, child) {
            final title =
                value > 90
                    ? context.loc.excellent
                    : value > 70
                    ? context.loc.good
                    : context.loc.needs_practice;
            return Text(
              title,
              style: Theme.of(context).textTheme.headlineLarge,
            );
          },
        ),
        TweenAnimationBuilder<int>(
          tween: IntTween(begin: 0, end: averageScore),
          duration: const Duration(milliseconds: 1800),
          builder: (context, value, child) {
            final desc =
                value > 90
                    ? context.loc.excellent_desc
                    : value > 70
                    ? context.loc.good_desc
                    : context.loc.needs_practice_desc;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                desc,
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: const Color(0xffB4A9A7),
                ),
              ),
            );
          },
        ),
        SizedBox(height: 24),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          width: MediaQuery.of(context).size.width - 48,
          child: Wrap(
            direction: Axis.horizontal,
            alignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            spacing: 16.0,
            runSpacing: 16.0,
            children: [
              _scoreValue(
                score: accuracyScore,
                description: context.loc.sound_match,
                context: context,
              ),
              _scoreValue(
                score: fluencyScore,
                description: context.loc.smooth_talk,
                context: context,
              ),
              _scoreValue(
                score: rhythmScore,
                description: context.loc.natural_flow,
                context: context,
              ),
            ],
          ),
        ), // Bottom padding
      ],
    );
  }

  Widget _scoreValue({
    required int score,
    required String description,
    required BuildContext context,
  }) {
    return Column(
      children: [
        ScoreProgressIndicator(score: score),
        SizedBox(height: 16),
        Text(description, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }
}

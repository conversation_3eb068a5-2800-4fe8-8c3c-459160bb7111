import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class SpeakingArenaOnboardingScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  const SpeakingArenaOnboardingScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<SpeakingArenaOnboardingScreen> createState() =>
      _SpeakingArenaOnboardingScreenState();
}

class _SpeakingArenaOnboardingScreenState
    extends ConsumerState<SpeakingArenaOnboardingScreen>
    with TickerProviderStateMixin {
  late AsyncValue<SpeakingState> viewState;
  late SpeakingController viewModel;
  late TabController _tabController;
  late Size _size;
  late List<DefaultModel> _listItem;
  bool _isInitialized = false;
  bool _isAnimationComplete = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed && !_isAnimationComplete) {
        _animationController.reverse();
      } else if (status == AnimationStatus.dismissed) {
        setState(() {
          _isAnimationComplete = true;
        });
        _animationController.forward();
      }
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void init() {
    _isInitialized = true;
    _size = MediaQuery.of(context).size;
    _listItem = [
      DefaultModel(
        title: context.loc.listen_and_follow,
        subtitle: context.loc.listen_and_follow_desc,
        image: '$assetImageMainLesson/speaking_arena/Ins-BG4.png',
      ),
      DefaultModel(
        title: context.loc.repeat_the_practice,
        subtitle: context.loc.repeat_the_practice_desc,
        image: '$assetImageMainLesson/speaking_arena/Ins-BG5.png',
      ),
    ];
    _tabController = TabController(length: _listItem.length, vsync: this)
      ..addListener(() {
        setState(() {});
      });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) init();

    final prov = speakingControllerProvider(
      widget.level,
      widget.chapter,
      'blankpath',
      SpeakingStage.stage1,
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    if (_isAnimationComplete && viewState.hasValue) {
      if (viewState.value!.isIntro) {
        customNav(
          context,
          RouterName.speakingArena,
          isReplace: true,
          params: {
            'level': widget.level,
            'chapter': widget.chapter,
            'path': 'blankpath',
          },
        );
      }
    }

    return FadeTransition(
      opacity: _animation,
      child: Scaffold(
        body: _isAnimationComplete ? _onboarding() : _transition(),
      ),
    );
  }

  Stack _onboarding() => Stack(
    children: [
      TabBarView(
        controller: _tabController,
        children:
            _listItem.map<Widget>((item) => _pageSlide(item: item)).toList(),
      ),
      Positioned(
        top: 76,
        child: Container(
          width: _size.width,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LinearProgressIndicator(
                value: (_tabController.index + 1) / _tabController.length,
                backgroundColor: const Color(0xffFFDAD2),
                valueColor: AlwaysStoppedAnimation(
                  Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 26),
              Text(
                context.loc.instructions,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(color: Colors.black),
                textAlign: TextAlign.start,
              ),
            ],
          ),
        ),
      ),
      Positioned(
        bottom: 72,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          width: _size.width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _navigationButton(
                onTap: () async {
                  if (_tabController.index == 0) {
                    context.pop();
                  } else {
                    _tabController.animateTo(_tabController.index - 1);
                  }
                },
                icon: Icons.chevron_left_rounded,
              ),
              _navigationButton(
                isLeft: false,
                onTap: () async {
                  if (_tabController.index < _listItem.length - 1) {
                    _tabController.animateTo(
                      _tabController.index + 1,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  } else {
                    customNav(
                      context,
                      RouterName.speakingArenaInstruction,
                      isReplace: true,
                      params: {
                        'level': widget.level,
                        'chapter': widget.chapter,
                      },
                    );
                  }
                },
                icon: Icons.chevron_right_rounded,
              ),
            ],
          ),
        ),
      ),
    ],
  );

  dynamic _transition() => _pageSlide(
    item: DefaultModel(
      title: context.loc.speakingArena,
      image: '$assetImageMainLesson/speaking_arena/BG20-Android.png',
    ),
  );

  InkWell _navigationButton({
    required VoidCallback onTap,
    required IconData icon,
    bool isLeft = true,
  }) => InkWell(
    onTap: onTap,
    child: Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: const Color(0xff6D2009), width: 0.8),
        gradient:
            isLeft
                ? null
                : const LinearGradient(
                  colors: [Color(0xffCA1E23), Color(0xff6D2009)],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                ),
      ),
      height: 50,
      width: 50,
      child: Icon(
        icon,
        size: 48,
        color: isLeft ? const Color(0xff93000F) : Colors.white,
      ),
    ),
  );

  Container _pageSlide({required DefaultModel item}) => Container(
    padding: const EdgeInsets.symmetric(horizontal: 46),
    child: ListView(
      children: [
        const SizedBox(height: 170),
        Container(
          height: 252,
          width: 252,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: const LinearGradient(
              colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            image: DecorationImage(
              image: AssetImage(item.image),
              fit: BoxFit.scaleDown,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          item.title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: MediaQuery.of(context).size.width - 62,
          child: Text(
            item.subtitle,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.labelLarge?.copyWith(color: const Color(0xff655C5A)),
          ),
        ),
      ],
    ),
  );
}

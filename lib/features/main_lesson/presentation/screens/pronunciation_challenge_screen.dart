import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/pronunciation_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/pronunciation_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/audio_record.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/image_soundwave.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/main_lesson_app_bar.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/dash_progress_indicator.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/pop_up_translate.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/recording_error_dialog.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class PronunciationChallengeScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const PronunciationChallengeScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<PronunciationChallengeScreen> createState() =>
      _PronunciationChallengeScreenState();
}

class _PronunciationChallengeScreenState
    extends ConsumerState<PronunciationChallengeScreen>
    with TickerProviderStateMixin {
  late AsyncValue<PronunciationState> viewState;
  late PronunciationController viewModel;

  final FirebaseStorage storage = FirebaseStorage.instance;
  bool _isAnimationComplete = false;
  late final AnimationController _animationController;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener(_handleAnimationStatus);

    _animationController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isAnimationComplete) {
      _animationController.reverse();
    } else if (status == AnimationStatus.dismissed) {
      if (mounted) {
        setState(() {
          _isAnimationComplete = true;
        });
      }
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final prov = pronunciationControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    ref.listen(prov, ((previous, next) {
      next.maybeWhen(
        error: (error, track) {
          if (mounted) {
            if (error.toString() ==
                "Please try again, we are unable to hear your voice.") {
              return showDialog(
                context: context,
                builder:
                    (context) => RecordingErrorDialog(
                      message: error.toString(),
                      onClose: () => Navigator.of(context).pop(),
                    ),
              );
            }
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(error.toString())));
          }
        },
        orElse: () {},
      );
    }));

    return switch (viewState) {
      AsyncData()
          when viewState.value != null &&
              viewState.value!.isNewSubpart &&
              !_isAnimationComplete =>
        _transition(),
      AsyncData() when viewState.value != null => _body(),
      AsyncError() => _body(),
      AsyncLoading() => const AppLoading(),
      _ => const SizedBox.shrink(),
    };
  }

  Widget _transition() => FadeTransition(
    opacity: _animation,
    child: Scaffold(
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 46),
        child: ListView(
          children: [
            const SizedBox(height: 82),
            if (viewState.value?.currentPath != null)
              Text(
                '${context.loc.part} ${viewState.value!.currentPath!.partOrder! + 1}',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            const SizedBox(height: 42),
            Container(
              height: 252,
              width: 252,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: AssetImage(
                    '$assetImageMainLesson/pronunciation_challenge/BG6-Android.png',
                  ),
                  fit: BoxFit.scaleDown,
                ),
              ),
            ),
            const SizedBox(height: 32),
            Center(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 5.5,
                  vertical: 2,
                ),
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  viewState.value?.currentPath?.partTitle ?? '',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              viewState.value?.currentPath?.subpartTitle ?? '',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ],
        ),
      ),
    ),
  );

  Widget _body() {
    if (viewState.value == null && viewState.value!.data == null) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      appBar: MainLessonAppBar(
        title: viewState.value?.currentPath?.partTitle ?? '',
        isBookmarked: viewState.value!.data?.isBookmarked ?? false,
        onBookmark: () {
          viewModel.saveBookmark();
        },
        onHelp: () {
          customNav(
            context,
            RouterName.pronunciationChallengeInstruction,
            isReplace: true,
            params: {'level': widget.level, 'chapter': widget.chapter},
          );
        },
      ),
      body: Stack(
        alignment: Alignment.center,
        children: [
          ListView(
            children: [
              const SizedBox(height: 24),
              DashProgressIndicator(
                progress: viewModel.index,
                totalLength: viewModel.totalPaths,
              ),
              const SizedBox(height: 24),
              if (viewState.value!.data != null)
                ImageSoundWave(
                  imageUrl: viewState.value!.data?.image ?? '',
                  audioUrl: viewState.value!.data?.audio ?? '',
                  isAudioEnabled: true,
                  // isLandscape: true,
                ),
              const SizedBox(height: 18),
              // Text Caption Container
              Container(
                margin: const EdgeInsets.all(16),
                padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                decoration: BoxDecoration(
                  color: Color(0xffFFEDEB),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.white),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.12),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        viewState.value!.data?.caption ?? '',
                        textAlign: TextAlign.left,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Color(0xff540005),
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.g_translate,
                        size: 32,
                        color: Color(0xff540005),
                      ),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => PopUpTranslate(
                                caption:
                                    viewState.value!.data?.translation ?? '',
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 18),
              Recorder(
                onStart: () async {
                  viewModel.changeRecordingState();
                  // await ref
                  //     .read(audioPlayerProvider)
                  //     .stop();
                },
                onStop: (path) {
                  if (mounted) {
                    viewModel.uploadAudio(path: path, context: context);
                    viewModel.changeRecordingState();
                  }
                },
                isLoading: viewState.value!.isLoading,
                isRecording: viewState.value!.isRecording,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/bookmark/domain/repositories/bookmark_repository.dart';
import 'package:selfeng/features/bookmark/presentation/providers/bookmark_provider.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'listening_controller.g.dart';

@riverpod
class ListeningController extends _$ListeningController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  late BookmarkRepository _bookmarkRepository;
  List<ContentIndexData> _paths = [];
  Set<String> _completedPaths = {};
  late MainLessonState _mainLessonState;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  @override
  FutureOr<ListeningState> build(
    String level,
    String chapter,
    String path,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _bookmarkRepository = ref.watch(bookmarkRepositoryProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );

    // Set Crashlytics context
    final crashlytics = ref.read(crashlyticsServiceProvider);
    await crashlytics.setCustomKeys({
      'feature': 'main_lesson',
      'section': 'listening',
      'level': level,
      'chapter': chapter,
    });
    crashlytics.log(
      'Listening lesson initialized - Level: $level, Chapter: $chapter',
    );

    late bool isIntroValue; // Renamed to avoid conflict

    await mainLessonRepository.isIntro(lessonName: 'listening').then((val) {
      val.fold(
        (failure) {
          // Report intro check error to Crashlytics
          final crashlytics = ref.read(crashlyticsServiceProvider);
          crashlytics
              .recordError(
                failure,
                StackTrace.current,
                reason: 'Listening Intro Check Failed',
                context: {
                  'category': 'listening_lesson',
                  'operation': 'check_intro',
                  'lesson_name': 'listening',
                  'level': level,
                  'chapter': chapter,
                  'path': path,
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          isIntroValue = data;
        },
      );
    });
    // Initialize with a default state that includes isIntroValue
    final initialState = ListeningState(
      isIntro: isIntroValue,
      expandedQuestionIndex: null,
    );
    state = AsyncData(initialState); // Ensure state is AsyncData before init

    await init(level, chapter, path); // await init to complete

    // After init, state.value might be updated. Return the final state.
    // If init sets state to AsyncError, this return might be overridden.
    // The build method should return the initial state or result of async work.
    // The init method updates the state internally.
    return state.value ??
        initialState; // Return current state value or the initial one if somehow still null
  }

  Future<void> init(String level, String chapter, String path) async {
    final contents = await mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.listening,
    );
    await mainLessonRepository.saveIntro(lessonName: 'listening');
    contents.fold(
      (failure) {
        // Report path index error to Crashlytics
        final crashlytics = ref.read(crashlyticsServiceProvider);
        crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Listening Path Index Fetch Failed',
              context: {
                'category': 'listening_lesson',
                'operation': 'get_path_index',
                'level': level,
                'chapter': chapter,
                'path': path,
                'section': 'listening',
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        _paths = data;

        final result = await _userDataServiceRepository.getListeningResult(
          PronunciationScoreParams(level: level, chapter: chapter),
        );

        result.fold(
          (failure) {
            // Report listening results fetch error
            final crashlytics = ref.read(crashlyticsServiceProvider);
            crashlytics
                .recordError(
                  failure,
                  StackTrace.current,
                  reason: 'Listening Results Fetch Failed',
                  context: {
                    'category': 'listening_lesson',
                    'operation': 'get_listening_results',
                    'level': level,
                    'chapter': chapter,
                  },
                  fatal: false,
                )
                .catchError((e) => debugPrint('Failed to report error: $e'));

            state = AsyncError(failure.message, StackTrace.current);
          },
          (data) {
            _completedPaths = data.map((e) => e.path).toSet();
          },
        );

        _mainLessonState = ref.read(mainLessonStateProvider);

        int initialPage = 0;
        if (path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          final idx = _paths.indexWhere(
            (element) => element.contentPath == contentPath,
          );
          if (idx != -1) {
            initialPage = idx;
          }
        } else if (_mainLessonState.fromLastCourse == true &&
            _mainLessonState.lastListening != null) {
          final idx = _paths.indexWhere(
            (element) =>
                element.contentPath == _mainLessonState.lastListening!.path,
          );
          if (idx != -1) {
            initialPage = idx;
          }
        }
        // Ensure state.value is not null before copying
        final currentVal = state.value ?? ListeningState();
        state = AsyncData(currentVal.copyWith(currentPage: initialPage));
        await initContent();
      },
    );
  }

  Future<void> initContent() async {
    if (_paths.isEmpty && state.value?.currentPage == 0) {
      // Avoid error if _paths is empty
      // Handle case where _paths might be empty, perhaps set an empty listenings list or error state
      final currentVal = state.value ?? ListeningState();
      state = AsyncData(currentVal.copyWith(listenings: []));
      return;
    }
    final contentFuture = mainLessonRepository.getListeningList(
      _paths.map((e) => e.contentPath).toList(),
    );

    // Instead of getting all bookmarks at once, we'll check each content item individually
    final List<Future<Either<AppException, bool>>> bookmarkFutures = [];
    for (final path in _paths) {
      bookmarkFutures.add(_bookmarkRepository.isBookmarked(path.contentPath));
    }

    final results = await Future.wait([contentFuture, Future.wait(bookmarkFutures)]);

    final contentResult =
        results[0] as Either<AppException, List<ListeningPart>>;
    final List<Either<AppException, bool>> bookmarkResults = results[1] as List<Either<AppException, bool>>;

    if (contentResult.isLeft()) {
      final failure = contentResult.fold((l) => l, (r) => null);

      // Report content fetch error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics
          .recordError(
            failure!,
            StackTrace.current,
            reason: 'Listening Content Fetch Failed',
            context: {
              'category': 'listening_lesson',
              'operation': 'get_listening_content',
              'paths_count': _paths.length,
              'error_type': failure.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(failure.message, StackTrace.current);
      return;
    }

    List<ListeningPart> contentData =
        contentResult.fold((l) => null, (r) => r)!;

    int? initialExpandedIdx;
    if (contentData.isNotEmpty &&
        state.value!.currentPage < contentData.length && // bounds check
        contentData[state.value!.currentPage].questions.isNotEmpty) {
      final currentQuestions = contentData[state.value!.currentPage].questions;
      final firstUnanswered = currentQuestions.indexWhere(
        (q) => q.answer == null,
      );
      if (firstUnanswered != -1) {
        initialExpandedIdx = firstUnanswered;
      } else {
        // All answered or no questions
        initialExpandedIdx = null; // Nothing to expand if all answered
      }

      // Update bookmark status for each content item
      for (int i = 0; i < contentData.length; i++) {
        final bookmarkResult = bookmarkResults[i];
        if (bookmarkResult.isRight()) {
          final isBookmarked = bookmarkResult.fold((l) => false, (r) => r);
          contentData[i] = contentData[i].copyWith(isBookmarked: isBookmarked);
        }
        // If there's an error checking bookmark status, we'll just keep the default (false)
      }
    } else {
      initialExpandedIdx = null;
      
      // Update bookmark status for each content item
      for (int i = 0; i < contentData.length; i++) {
        final bookmarkResult = bookmarkResults[i];
        if (bookmarkResult.isRight()) {
          final isBookmarked = bookmarkResult.fold((l) => false, (r) => r);
          contentData[i] = contentData[i].copyWith(isBookmarked: isBookmarked);
        }
        // If there's an error checking bookmark status, we'll just keep the default (false)
      }
    }
    
    final currentVal = state.value ?? ListeningState();
    state = AsyncData(
      currentVal.copyWith(
        listenings: contentData,
        expandedQuestionIndex: initialExpandedIdx,
      ),
    );

    // Log successful content initialization
    final crashlytics = ref.read(crashlyticsServiceProvider);
    crashlytics.log(
      'Listening content initialized - Parts: ${contentData.length}',
    );

    await saveLastCourse();
  }

  void selectAnswer({required int questionIndex, required String value}) {
    if (state.value == null) return; // Guard against null state
    int answerChoiceIndex = alphabetToIndex(value);

    final currentPageIndex = state.value!.currentPage;
    final listeningsList = state.value!.listenings;

    if (currentPageIndex < listeningsList.length &&
        questionIndex < listeningsList[currentPageIndex].questions.length) {
      List<ListeningPart> tempData = List.from(listeningsList);
      List<Question> tempList = List.from(tempData[currentPageIndex].questions);

      tempList[questionIndex] = tempList[questionIndex].copyWith(
        answer: value,
        isCorrect:
            tempData[currentPageIndex]
                .questions[questionIndex]
                .choices[answerChoiceIndex]
                .isCorrect,
      );
      tempData[currentPageIndex] = tempData[currentPageIndex].copyWith(
        questions: tempList,
      );

      int? nextUnansweredIndex;
      final questionsInCurrentPart = tempData[currentPageIndex].questions;
      for (int i = 0; i < questionsInCurrentPart.length; i++) {
        if (questionsInCurrentPart[i].answer == null) {
          nextUnansweredIndex = i;
          break;
        }
      }
      state = AsyncData(
        state.value!.copyWith(
          listenings: tempData,
          expandedQuestionIndex: nextUnansweredIndex,
        ),
      );
    }
  }

  void setExpandedQuestion(int? questionIndexToExpand) {
    if (state.value == null) return;

    int? newExpandedIndex;

    if (questionIndexToExpand != null) {
      final currentQuestions =
          state.value!.listenings[state.value!.currentPage].questions;
      if (questionIndexToExpand >= 0 &&
          questionIndexToExpand < currentQuestions.length) {
        // Only allow expanding if not answered
        if (currentQuestions[questionIndexToExpand].answer == null) {
          newExpandedIndex =
              (state.value!.expandedQuestionIndex == questionIndexToExpand)
                  ? null
                  : questionIndexToExpand;
        } else {
          // If trying to interact with an answered question, ensure nothing is expanded (or keep current if it's different)
          newExpandedIndex =
              state.value!.expandedQuestionIndex == questionIndexToExpand
                  ? null
                  : state.value!.expandedQuestionIndex;
          if (currentQuestions[questionIndexToExpand].answer != null &&
              state.value!.expandedQuestionIndex == questionIndexToExpand) {
            newExpandedIndex =
                null; // Collapse if it was an answered one that was somehow marked expanded
          }
        }
      } else {
        newExpandedIndex =
            state
                .value!
                .expandedQuestionIndex; // Keep current if index out of bounds
      }
    } else {
      // explicitly asked to collapse (questionIndexToExpand is null)
      newExpandedIndex = null;
    }

    state = AsyncData(
      state.value!.copyWith(expandedQuestionIndex: newExpandedIndex),
    );
  }

  Future<void> nextPart(BuildContext context) async {
    if (state.value == null) return;
    final currentPage = state.value!.currentPage;
    final listenings = state.value!.listenings;

    await saveLastCourse();

    if (currentPage < listenings.length - 1) {
      // Prepare the next part question index
      final nextPartQuestions = listenings[currentPage + 1].questions;
      int? nextPartExpandedIdx;
      if (nextPartQuestions.isNotEmpty) {
        final firstUnansweredInNext = nextPartQuestions.indexWhere(
          (q) => q.answer == null,
        );
        nextPartExpandedIdx =
            (firstUnansweredInNext != -1)
                ? firstUnansweredInNext
                : null; // Expand first unanswered, or null if all answered
      } else {
        nextPartExpandedIdx = null;
      }
      // Move to next part
      state = AsyncData(
        state.value!.copyWith(
          currentPage: currentPage + 1,
          isNewPart: true,
          expandedQuestionIndex: nextPartExpandedIdx,
        ),
      );

      customNav(
        context,
        RouterName.listeningMastery,
        isReplace: true,
        params: {'level': level, 'chapter': chapter, 'path': path},
      );
    } else {
      // End of all parts
      state = AsyncData(state.value!.copyWith(nextSection: true));
    }
  }

  Future<void> saveLastCourse() async {
    if (state.value == null ||
        _paths.isEmpty ||
        state.value!.currentPage >= _paths.length) {
      return;
    }

    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level, // level, chapter are build parameters
      chapter: int.parse(chapter),
      section: SectionType.listening,
      path: _paths[state.value!.currentPage].contentPath,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.listening,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastListening(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  String indexToAlphabet(int number) {
    switch (number) {
      case 0:
        return 'A';
      case 1:
        return 'B';
      case 2:
        return 'C';
      case 3:
        return 'D';
      default:
        return '';
    }
  }

  int alphabetToIndex(String alphabet) {
    switch (alphabet) {
      case 'A':
        return 0;
      case 'B':
        return 1;
      case 'C':
        return 2;
      case 'D':
        return 3;
      default:
        return -1;
    }
  }

  int getCorrectChoiceIndex() {
    // This would need a question index if used
    // This method seems unused in the current screen logic after changes.
    // If needed, it should operate on a specific question.
    return -1;
  }

  int calculateTotalScore() {
    if (state.value == null) return 0;
    int score = 0;
    final listenings = state.value!.listenings;
    for (var page in listenings) {
      for (var question in page.questions) {
        if (question.isCorrect ?? false) {
          score++;
        }
      }
    }
    return score;
  }

  int calculateTotalScorePart() {
    if (state.value == null ||
        state.value!.listenings.isEmpty ||
        state.value!.currentPage >= state.value!.listenings.length) {
      return 0;
    }
    int correctAnswers = calculateTotalCorrectPart();
    final questionsInPart = state.value!.currentListenings.questions;
    if (questionsInPart.isEmpty) return 0;
    return (correctAnswers / questionsInPart.length * 100).round();
  }

  int calculateTotalCorrectPart() {
    if (state.value == null ||
        state.value!.listenings.isEmpty ||
        state.value!.currentPage >= state.value!.listenings.length) {
      return 0;
    }
    int score = 0;
    final questionsInPart = state.value!.currentListenings.questions;
    for (var question in questionsInPart) {
      if (question.isCorrect ?? false) {
        score++;
      }
    }
    return score;
  }

  Future<void> saveResult() async {
    if (state.value == null ||
        _paths.isEmpty ||
        state.value!.currentPage >= _paths.length) {
      return;
    }

    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.listening,
      result: LessonResult(
        contentOrder: _paths[state.value!.currentPage].contentOrder,
        path: _paths[state.value!.currentPage].contentPath,
        result: {
          'correct': calculateTotalCorrectPart(),
          'total': state.value!.currentListenings.questions.length,
        },
      ),
    );

    result.fold(
      (failure) {
        // Report save result error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Listening Result Save Failed',
              context: {
                'category': 'listening_lesson',
                'operation': 'save_result',
                'level': level,
                'chapter': chapter,
                'content_order': _paths[state.value!.currentPage].contentOrder,
                'path': _paths[state.value!.currentPage].contentPath,
                'correct': calculateTotalCorrectPart(),
                'total': state.value!.currentListenings.questions.length,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        // Log successful result save
        final crashlytics = ref.read(crashlyticsServiceProvider);
        crashlytics.log(
          'Listening result saved - Path: ${_paths[state.value!.currentPage].contentPath}',
        );

        _completedPaths.add(_paths[state.value!.currentPage].contentPath);
        _chapterContentStateNotifier.updateListening(
          _paths[state.value!.currentPage],
        );
        markSectionAsCompleted();
      },
    );
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m)
    // Create a set of required paths and check if it's a subset of completed paths
    final requiredPaths =
        _paths.map((pathData) => pathData.contentPath).toSet();
    final allPathsCompleted = requiredPaths.difference(_completedPaths).isEmpty;

    if (allPathsCompleted) {
      // Log section completion
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics.log(
        'Listening section completed - Level: $level, Chapter: $chapter',
      );
      await crashlytics.setCustomKeys({
        'last_section_completed': 'listening',
        'completed_level': level,
        'completed_chapter': chapter,
        'section_completed_at': DateTime.now().toIso8601String(),
      });

      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.listening,
      );
    }
  }

  void resetNextSectionState() {
    if (state.value != null) {
      state = AsyncData(state.value!.copyWith(nextSection: false));
    }
  }

  Future<void> saveBookmark() async {
    try {
      // Get the current bookmark status
      final contentPath = _paths[state.value!.currentPage].contentPath;
      final bookmarkCheckResult = await _bookmarkRepository.isBookmarked(contentPath);

      bookmarkCheckResult.fold(
        (failure) {
          // Report bookmark save error
          final crashlytics = ref.read(crashlyticsServiceProvider);
          crashlytics
              .recordError(
                failure,
                StackTrace.current,
                reason: 'Listening Bookmark Save Failed',
                context: {
                  'category': 'listening_lesson',
                  'operation': 'isBookmarked',
                  'section': 'listening',
                  'path': contentPath,
                  'error_type': failure.runtimeType.toString(),
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(failure.message, StackTrace.empty);
        },
        (isCurrentlyBookmarked) async {
          if (isCurrentlyBookmarked) {
            // Remove bookmark
            final deleteResult = await _bookmarkRepository.deleteBookmark(contentPath);
            deleteResult.fold(
              (failure) {
                // Report bookmark delete error
                final crashlytics = ref.read(crashlyticsServiceProvider);
                crashlytics
                    .recordError(
                      failure,
                      StackTrace.current,
                      reason: 'Listening Bookmark Delete Failed',
                      context: {
                        'category': 'listening_lesson',
                        'operation': 'deleteBookmark',
                        'section': 'listening',
                        'path': contentPath,
                        'error_type': failure.runtimeType.toString(),
                      },
                      fatal: false,
                    )
                    .catchError((e) => debugPrint('Failed to report error: $e'));

                state = AsyncError(failure.message, StackTrace.empty);
              },
              (data) {
                // Successfully removed bookmark
                var listenings = state.value!.listenings;
                listenings = List.from(listenings);
                listenings[state.value!.currentPage] = listenings[
                        state.value!.currentPage]
                    .copyWith(isBookmarked: false);
                // Update the current state with the new bookmark status
                state = AsyncData(state.value!.copyWith(listenings: listenings));

                _paths[state.value!.currentPage] = _paths[state.value!.currentPage]
                    .copyWith(isBookmarked: false);
                _chapterContentStateNotifier.updateListening(
                  _paths[state.value!.currentPage],
                );
              },
            );
          } else {
            // Add bookmark
            // Get the title from the current content
            final title = state.value!.listenings[state.value!.currentPage].title ?? 'Listening Exercise';
            
            final saveResult = await _bookmarkRepository.saveBookmark(
              contentPath: contentPath,
              section: 'listening',
              title: title,
              level: level,
              chapter: int.parse(chapter),
            );

            saveResult.fold(
              (failure) {
                // Report bookmark save error
                final crashlytics = ref.read(crashlyticsServiceProvider);
                crashlytics
                    .recordError(
                      failure,
                      StackTrace.current,
                      reason: 'Listening Bookmark Save Failed',
                      context: {
                        'category': 'listening_lesson',
                        'operation': 'saveBookmark',
                        'section': 'listening',
                        'path': contentPath,
                        'error_type': failure.runtimeType.toString(),
                      },
                      fatal: false,
                    )
                    .catchError((e) => debugPrint('Failed to report error: $e'));

                state = AsyncError(failure.message, StackTrace.empty);
              },
              (data) {
                // Successfully added bookmark
                var listenings = state.value!.listenings;
                listenings = List.from(listenings);
                listenings[state.value!.currentPage] = listenings[
                        state.value!.currentPage]
                    .copyWith(isBookmarked: true);
                // Update the current state with the new bookmark status
                state = AsyncData(state.value!.copyWith(listenings: listenings));

                _paths[state.value!.currentPage] = _paths[state.value!.currentPage]
                    .copyWith(isBookmarked: true);
                _chapterContentStateNotifier.updateListening(
                  _paths[state.value!.currentPage],
                );
              },
            );
          }
        },
      );
    } catch (error, stackTrace) {
      // Report unexpected bookmark save error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Unexpected Listening Bookmark Save Error',
            context: {
              'category': 'listening_lesson',
              'operation': 'saveBookmark',
              'section': 'listening',
              'page_index': state.value!.currentPage.toString(),
              'error_type': error.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(error.toString(), stackTrace);
    }
  }
}

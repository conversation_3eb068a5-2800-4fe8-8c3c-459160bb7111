import 'package:flutter/material.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/shared/helpers/time_helper.dart';

class BookmarkCard extends StatelessWidget {
  final Bookmark bookmark;

  const BookmarkCard({super.key, required this.bookmark});

  @override
  Widget build(BuildContext context) {
    final sectionIcon = _getSectionIcon(bookmark.section);
    final sectionColor = _getSectionColor(bookmark.section);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Icon(sectionIcon, color: sectionColor),
        title: Text(bookmark.title),
        subtitle: Text(
          '${_formatLevel(bookmark.level)} • Chapter ${bookmark.chapter}',
        ),
        trailing: Text(_formatTimestamp(bookmark.timestamp)),
        onTap: () {
          // TODO: Navigate to the original content
          // This would require implementing deep linking to the content
        },
      ),
    );
  }

  IconData _getSectionIcon(String section) {
    switch (section) {
      case 'pronunciation':
        return Icons.record_voice_over;
      case 'conversation':
        return Icons.chat;
      case 'listening':
        return Icons.headphones;
      case 'speaking':
        return Icons.mic;
      default:
        return Icons.bookmark;
    }
  }

  Color _getSectionColor(String section) {
    switch (section) {
      case 'pronunciation':
        return const Color(0xFFFF6B6B);
      case 'conversation':
        return const Color(0xFF4ECDC4);
      case 'listening':
        return const Color(0xFF45B7D1);
      case 'speaking':
        return const Color(0xFFE6A742);
      default:
        return Colors.grey;
    }
  }

  String _formatLevel(String level) {
    // Capitalize first letter
    if (level.isEmpty) return level;
    return '${level[0].toUpperCase()}${level.substring(1)}';
  }

  String _formatTimestamp(DateTime timestamp) {
    return TimeHelper.formatTimeAgo(timestamp);
  }
}
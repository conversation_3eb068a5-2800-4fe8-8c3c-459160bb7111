import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/bookmark/data/datasources/bookmark_firestore_datasource_impl.dart';
import 'package:selfeng/features/bookmark/data/repositories/bookmark_repository_impl.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/features/bookmark/domain/repositories/bookmark_repository.dart';

part 'bookmark_provider.g.dart';

@riverpod
BookmarkRepository bookmarkRepository(Ref ref) {
  final datasource = BookmarkFirestoreDatasourceImpl();
  return BookmarkRepositoryImpl(datasource);
}

@riverpod
class BookmarkList extends _$BookmarkList {
  @override
  Future<List<Bookmark>> build() async {
    // Load initial bookmarks automatically
    final repository = ref.read(bookmarkRepositoryProvider);
    final result = await repository.getBookmarks(limit: 20);

    return result.fold((error) => throw error, (bookmarks) => bookmarks);
  }

  Future<void> loadBookmarks({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? section,
  }) async {
    final repository = ref.read(bookmarkRepositoryProvider);
    final result = await repository.getBookmarks(
      limit: limit,
      startAfter: startAfter,
      section: section,
    );

    result.fold(
      (error) {
        // Handle error
        state = AsyncError(error, StackTrace.current);
      },
      (bookmarks) {
        // If startAfter is provided, append to existing bookmarks
        if (startAfter != null) {
          state = AsyncData([...?state.value, ...bookmarks]);
        } else {
          state = AsyncData(bookmarks);
        }
      },
    );
  }

  Future<void> refresh() async {
    state = const AsyncLoading();
    await loadBookmarks();
  }
}

@riverpod
class BookmarkFilter extends _$BookmarkFilter {
  @override
  String? build() {
    return null; // No filter by default
  }

  void setFilter(String? section) {
    state = section;
  }
}

@riverpod
class BookmarkSearch extends _$BookmarkSearch {
  @override
  String build() {
    return ''; // Empty search by default
  }

  void setSearch(String search) {
    state = search;
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word_scramble_game.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WordScrambleGame _$WordScrambleGameFromJson(Map<String, dynamic> json) =>
    _WordScrambleGame(
      word: json['word'] as String?,
      scrambledLetters: (json['scrambledLetters'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      selectedLetters: (json['selectedLetters'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isCompleted: json['isCompleted'] as bool? ?? false,
      score: (json['score'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WordScrambleGameToJson(_WordScrambleGame instance) =>
    <String, dynamic>{
      'word': instance.word,
      'scrambledLetters': instance.scrambledLetters,
      'selectedLetters': instance.selectedLetters,
      'isCompleted': instance.isCompleted,
      'score': instance.score,
    };

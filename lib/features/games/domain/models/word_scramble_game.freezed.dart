// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'word_scramble_game.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WordScrambleGame {

 String? get word; List<String>? get scrambledLetters; List<String>? get selectedLetters; bool get isCompleted; int? get score;
/// Create a copy of WordScrambleGame
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WordScrambleGameCopyWith<WordScrambleGame> get copyWith => _$WordScrambleGameCopyWithImpl<WordScrambleGame>(this as WordScrambleGame, _$identity);

  /// Serializes this WordScrambleGame to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WordScrambleGame&&(identical(other.word, word) || other.word == word)&&const DeepCollectionEquality().equals(other.scrambledLetters, scrambledLetters)&&const DeepCollectionEquality().equals(other.selectedLetters, selectedLetters)&&(identical(other.isCompleted, isCompleted) || other.isCompleted == isCompleted)&&(identical(other.score, score) || other.score == score));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,word,const DeepCollectionEquality().hash(scrambledLetters),const DeepCollectionEquality().hash(selectedLetters),isCompleted,score);

@override
String toString() {
  return 'WordScrambleGame(word: $word, scrambledLetters: $scrambledLetters, selectedLetters: $selectedLetters, isCompleted: $isCompleted, score: $score)';
}


}

/// @nodoc
abstract mixin class $WordScrambleGameCopyWith<$Res>  {
  factory $WordScrambleGameCopyWith(WordScrambleGame value, $Res Function(WordScrambleGame) _then) = _$WordScrambleGameCopyWithImpl;
@useResult
$Res call({
 String? word, List<String>? scrambledLetters, List<String>? selectedLetters, bool isCompleted, int? score
});




}
/// @nodoc
class _$WordScrambleGameCopyWithImpl<$Res>
    implements $WordScrambleGameCopyWith<$Res> {
  _$WordScrambleGameCopyWithImpl(this._self, this._then);

  final WordScrambleGame _self;
  final $Res Function(WordScrambleGame) _then;

/// Create a copy of WordScrambleGame
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? word = freezed,Object? scrambledLetters = freezed,Object? selectedLetters = freezed,Object? isCompleted = null,Object? score = freezed,}) {
  return _then(_self.copyWith(
word: freezed == word ? _self.word : word // ignore: cast_nullable_to_non_nullable
as String?,scrambledLetters: freezed == scrambledLetters ? _self.scrambledLetters : scrambledLetters // ignore: cast_nullable_to_non_nullable
as List<String>?,selectedLetters: freezed == selectedLetters ? _self.selectedLetters : selectedLetters // ignore: cast_nullable_to_non_nullable
as List<String>?,isCompleted: null == isCompleted ? _self.isCompleted : isCompleted // ignore: cast_nullable_to_non_nullable
as bool,score: freezed == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [WordScrambleGame].
extension WordScrambleGamePatterns on WordScrambleGame {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WordScrambleGame value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WordScrambleGame() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WordScrambleGame value)  $default,){
final _that = this;
switch (_that) {
case _WordScrambleGame():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WordScrambleGame value)?  $default,){
final _that = this;
switch (_that) {
case _WordScrambleGame() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? word,  List<String>? scrambledLetters,  List<String>? selectedLetters,  bool isCompleted,  int? score)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WordScrambleGame() when $default != null:
return $default(_that.word,_that.scrambledLetters,_that.selectedLetters,_that.isCompleted,_that.score);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? word,  List<String>? scrambledLetters,  List<String>? selectedLetters,  bool isCompleted,  int? score)  $default,) {final _that = this;
switch (_that) {
case _WordScrambleGame():
return $default(_that.word,_that.scrambledLetters,_that.selectedLetters,_that.isCompleted,_that.score);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? word,  List<String>? scrambledLetters,  List<String>? selectedLetters,  bool isCompleted,  int? score)?  $default,) {final _that = this;
switch (_that) {
case _WordScrambleGame() when $default != null:
return $default(_that.word,_that.scrambledLetters,_that.selectedLetters,_that.isCompleted,_that.score);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WordScrambleGame implements WordScrambleGame {
   _WordScrambleGame({this.word, final  List<String>? scrambledLetters, final  List<String>? selectedLetters, this.isCompleted = false, this.score}): _scrambledLetters = scrambledLetters,_selectedLetters = selectedLetters;
  factory _WordScrambleGame.fromJson(Map<String, dynamic> json) => _$WordScrambleGameFromJson(json);

@override final  String? word;
 final  List<String>? _scrambledLetters;
@override List<String>? get scrambledLetters {
  final value = _scrambledLetters;
  if (value == null) return null;
  if (_scrambledLetters is EqualUnmodifiableListView) return _scrambledLetters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _selectedLetters;
@override List<String>? get selectedLetters {
  final value = _selectedLetters;
  if (value == null) return null;
  if (_selectedLetters is EqualUnmodifiableListView) return _selectedLetters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey() final  bool isCompleted;
@override final  int? score;

/// Create a copy of WordScrambleGame
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WordScrambleGameCopyWith<_WordScrambleGame> get copyWith => __$WordScrambleGameCopyWithImpl<_WordScrambleGame>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WordScrambleGameToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WordScrambleGame&&(identical(other.word, word) || other.word == word)&&const DeepCollectionEquality().equals(other._scrambledLetters, _scrambledLetters)&&const DeepCollectionEquality().equals(other._selectedLetters, _selectedLetters)&&(identical(other.isCompleted, isCompleted) || other.isCompleted == isCompleted)&&(identical(other.score, score) || other.score == score));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,word,const DeepCollectionEquality().hash(_scrambledLetters),const DeepCollectionEquality().hash(_selectedLetters),isCompleted,score);

@override
String toString() {
  return 'WordScrambleGame(word: $word, scrambledLetters: $scrambledLetters, selectedLetters: $selectedLetters, isCompleted: $isCompleted, score: $score)';
}


}

/// @nodoc
abstract mixin class _$WordScrambleGameCopyWith<$Res> implements $WordScrambleGameCopyWith<$Res> {
  factory _$WordScrambleGameCopyWith(_WordScrambleGame value, $Res Function(_WordScrambleGame) _then) = __$WordScrambleGameCopyWithImpl;
@override @useResult
$Res call({
 String? word, List<String>? scrambledLetters, List<String>? selectedLetters, bool isCompleted, int? score
});




}
/// @nodoc
class __$WordScrambleGameCopyWithImpl<$Res>
    implements _$WordScrambleGameCopyWith<$Res> {
  __$WordScrambleGameCopyWithImpl(this._self, this._then);

  final _WordScrambleGame _self;
  final $Res Function(_WordScrambleGame) _then;

/// Create a copy of WordScrambleGame
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? word = freezed,Object? scrambledLetters = freezed,Object? selectedLetters = freezed,Object? isCompleted = null,Object? score = freezed,}) {
  return _then(_WordScrambleGame(
word: freezed == word ? _self.word : word // ignore: cast_nullable_to_non_nullable
as String?,scrambledLetters: freezed == scrambledLetters ? _self._scrambledLetters : scrambledLetters // ignore: cast_nullable_to_non_nullable
as List<String>?,selectedLetters: freezed == selectedLetters ? _self._selectedLetters : selectedLetters // ignore: cast_nullable_to_non_nullable
as List<String>?,isCompleted: null == isCompleted ? _self.isCompleted : isCompleted // ignore: cast_nullable_to_non_nullable
as bool,score: freezed == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on

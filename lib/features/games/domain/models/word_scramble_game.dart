import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_scramble_game.freezed.dart';
part 'word_scramble_game.g.dart';

// Simple model class without freezed for now to avoid generation issues
@freezed
sealed class WordScrambleGame with _$WordScrambleGame {
  factory WordScrambleGame({
    String? word,
    List<String>? scrambledLetters,
    List<String>? selectedLetters,
    @Default(false) bool isCompleted,
    int? score,
  }) = _WordScrambleGame;
  factory WordScrambleGame.fromJson(Map<String, dynamic> json) =>
      _$WordScrambleGameFromJson(json);
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word_scramble_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(WordScrambleController)
const wordScrambleControllerProvider = WordScrambleControllerFamily._();

final class WordScrambleControllerProvider
    extends
        $AsyncNotifierProvider<WordScrambleController, WordScrambleGameState> {
  const WordScrambleControllerProvider._({
    required WordScrambleControllerFamily super.from,
    required String? super.argument,
  }) : super(
         retry: null,
         name: r'wordScrambleControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$wordScrambleControllerHash();

  @override
  String toString() {
    return r'wordScrambleControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  WordScrambleController create() => WordScrambleController();

  @override
  bool operator ==(Object other) {
    return other is WordScrambleControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$wordScrambleControllerHash() =>
    r'f4a42b9d8af2a583de35a23940f7085055302722';

final class WordScrambleControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          WordScrambleController,
          AsyncValue<WordScrambleGameState>,
          WordScrambleGameState,
          FutureOr<WordScrambleGameState>,
          String?
        > {
  const WordScrambleControllerFamily._()
    : super(
        retry: null,
        name: r'wordScrambleControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  WordScrambleControllerProvider call({String? topic}) =>
      WordScrambleControllerProvider._(argument: topic, from: this);

  @override
  String toString() => r'wordScrambleControllerProvider';
}

abstract class _$WordScrambleController
    extends $AsyncNotifier<WordScrambleGameState> {
  late final _$args = ref.$arg as String?;
  String? get topic => _$args;

  FutureOr<WordScrambleGameState> build({String? topic});
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(topic: _$args);
    final ref =
        this.ref
            as $Ref<AsyncValue<WordScrambleGameState>, WordScrambleGameState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<WordScrambleGameState>,
                WordScrambleGameState
              >,
              AsyncValue<WordScrambleGameState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

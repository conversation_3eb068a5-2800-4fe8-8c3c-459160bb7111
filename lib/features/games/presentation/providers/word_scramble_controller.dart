import 'dart:async';
import 'dart:math';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/games/domain/models/word_scramble_game.dart';
import 'package:selfeng/features/games/domain/repositories/game_repository.dart';
import 'package:selfeng/features/games/presentation/providers/state/word_scramble_state.dart';

part 'word_scramble_controller.g.dart';

@riverpod
class WordScrambleController extends _$WordScrambleController {
  late GameRepository gameRepository;
  @override
  FutureOr<WordScrambleGameState> build({String? topic}) async {
    return _initializeGame();
    return WordScrambleGameState(
      wordList: const [],
      currentWordIndex: 0,
      currentGame: WordScrambleGame(
        word: '',
        scrambledLetters: const [],
        selectedLetters: const [],
      ),
    );
  }

  final List<String> _wordList = [
    'FLUTTER',
    'DART',
    'MOBILE',
    'DEVELOPER',
    'CODING',
    'PROGRAMMING',
    'APPLICATION',
    'SOFTWARE',
    'INTERFACE',
    'DESIGN',
  ];

  WordScrambleGameState _initializeGame() {
    final shuffledWords = [..._wordList]..shuffle(Random());
    final currentWord = shuffledWords.first;
    final scrambledLetters = _scrambleWord(currentWord);

    return WordScrambleGameState(
      wordList: shuffledWords,
      currentWordIndex: 0,
      currentGame: WordScrambleGame(
        word: currentWord,
        scrambledLetters: scrambledLetters,
        selectedLetters: [],
      ),
    );
  }

  List<String> _scrambleWord(String word) {
    final letters = word.split('');
    letters.shuffle(Random());
    return letters;
  }

  void selectLetter(int index) {
    final currentGame = state.value!.currentGame;
    final scrambledLetters = List<String>.from(
      currentGame!.scrambledLetters ?? [],
    );
    final selectedLetters = List<String>.from(
      currentGame.selectedLetters ?? [],
    );

    if (index >= 0 && index < scrambledLetters.length) {
      final letter = scrambledLetters[index];
      selectedLetters.add(letter);
      scrambledLetters.removeAt(index);

      final updatedGame = currentGame.copyWith(
        scrambledLetters: scrambledLetters,
        selectedLetters: selectedLetters,
      );

      state = AsyncData(state.value!.copyWith(currentGame: updatedGame));
      checkWord();
    }
  }

  void unselectLetter(int index) {
    final currentGame = state.value!.currentGame;
    final scrambledLetters = List<String>.from(
      currentGame!.scrambledLetters ?? [],
    );
    final selectedLetters = List<String>.from(
      currentGame.selectedLetters ?? [],
    );

    if (index >= 0 && index < selectedLetters.length) {
      final letter = selectedLetters[index];
      scrambledLetters.add(letter);
      selectedLetters.removeAt(index);

      final updatedGame = currentGame.copyWith(
        scrambledLetters: scrambledLetters,
        selectedLetters: selectedLetters,
      );

      state = AsyncData(state.value!.copyWith(currentGame: updatedGame));
    }
  }

  void checkWord() {
    final currentGame = state.value!.currentGame;
    final selectedWord = currentGame!.selectedLetters!.join('');

    if (selectedWord.length == currentGame.word!.length) {
      final score = currentGame.word!.length * 10;
      final updatedGame = currentGame.copyWith(isCompleted: true, score: score);

      final totalScore = (state.value?.totalScore ?? 0) + score;
      state = AsyncData(
        state.value!.copyWith(currentGame: updatedGame, totalScore: totalScore),
      );

      // Move to next word if available
      if (state.value!.currentWordIndex! < state.value!.wordList!.length - 1) {
        Future.delayed(const Duration(seconds: 1), () {
          nextWord();
        });
      } else {
        state = AsyncData(state.value!.copyWith(isGameCompleted: true));
      }
    }
  }

  void nextWord() {
    final nextIndex = state.value!.currentWordIndex ?? 0 + 1;
    if (nextIndex < state.value!.wordList!.length) {
      final nextWord = state.value!.wordList![nextIndex];
      final scrambledLetters = _scrambleWord(nextWord);

      final newGame = WordScrambleGame(
        word: nextWord,
        scrambledLetters: scrambledLetters,
        selectedLetters: [],
      );

      state = AsyncData(
        state.value!.copyWith(
          currentWordIndex: nextIndex,
          currentGame: newGame,
        ),
      );
    }
  }

  void resetGame() {
    _initializeGame();
  }

  void exitGame() {
    // Handle any cleanup or state saving before exiting
  }
}

// observe_recall_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/games/domain/models/word_scramble_game.dart';

part 'word_scramble_state.freezed.dart';

enum GameStage { topic, loading, observe, recall, result, finished, gameOver }

@freezed
sealed class WordScrambleGameState with _$WordScrambleGameState {
  factory WordScrambleGameState({
    List<String>? wordList,
    int? currentWordIndex,
    WordScrambleGame? currentGame,
    int? totalScore,
    @Default(false) bool isGameCompleted,
  }) = _WordScrambleGameState;
}

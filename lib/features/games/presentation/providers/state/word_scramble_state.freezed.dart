// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'word_scramble_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WordScrambleGameState {

 List<String>? get wordList; int? get currentWordIndex; WordScrambleGame? get currentGame; int? get totalScore; bool get isGameCompleted;
/// Create a copy of WordScrambleGameState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WordScrambleGameStateCopyWith<WordScrambleGameState> get copyWith => _$WordScrambleGameStateCopyWithImpl<WordScrambleGameState>(this as WordScrambleGameState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WordScrambleGameState&&const DeepCollectionEquality().equals(other.wordList, wordList)&&(identical(other.currentWordIndex, currentWordIndex) || other.currentWordIndex == currentWordIndex)&&(identical(other.currentGame, currentGame) || other.currentGame == currentGame)&&(identical(other.totalScore, totalScore) || other.totalScore == totalScore)&&(identical(other.isGameCompleted, isGameCompleted) || other.isGameCompleted == isGameCompleted));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(wordList),currentWordIndex,currentGame,totalScore,isGameCompleted);

@override
String toString() {
  return 'WordScrambleGameState(wordList: $wordList, currentWordIndex: $currentWordIndex, currentGame: $currentGame, totalScore: $totalScore, isGameCompleted: $isGameCompleted)';
}


}

/// @nodoc
abstract mixin class $WordScrambleGameStateCopyWith<$Res>  {
  factory $WordScrambleGameStateCopyWith(WordScrambleGameState value, $Res Function(WordScrambleGameState) _then) = _$WordScrambleGameStateCopyWithImpl;
@useResult
$Res call({
 List<String>? wordList, int? currentWordIndex, WordScrambleGame? currentGame, int? totalScore, bool isGameCompleted
});


$WordScrambleGameCopyWith<$Res>? get currentGame;

}
/// @nodoc
class _$WordScrambleGameStateCopyWithImpl<$Res>
    implements $WordScrambleGameStateCopyWith<$Res> {
  _$WordScrambleGameStateCopyWithImpl(this._self, this._then);

  final WordScrambleGameState _self;
  final $Res Function(WordScrambleGameState) _then;

/// Create a copy of WordScrambleGameState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? wordList = freezed,Object? currentWordIndex = freezed,Object? currentGame = freezed,Object? totalScore = freezed,Object? isGameCompleted = null,}) {
  return _then(_self.copyWith(
wordList: freezed == wordList ? _self.wordList : wordList // ignore: cast_nullable_to_non_nullable
as List<String>?,currentWordIndex: freezed == currentWordIndex ? _self.currentWordIndex : currentWordIndex // ignore: cast_nullable_to_non_nullable
as int?,currentGame: freezed == currentGame ? _self.currentGame : currentGame // ignore: cast_nullable_to_non_nullable
as WordScrambleGame?,totalScore: freezed == totalScore ? _self.totalScore : totalScore // ignore: cast_nullable_to_non_nullable
as int?,isGameCompleted: null == isGameCompleted ? _self.isGameCompleted : isGameCompleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of WordScrambleGameState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WordScrambleGameCopyWith<$Res>? get currentGame {
    if (_self.currentGame == null) {
    return null;
  }

  return $WordScrambleGameCopyWith<$Res>(_self.currentGame!, (value) {
    return _then(_self.copyWith(currentGame: value));
  });
}
}


/// Adds pattern-matching-related methods to [WordScrambleGameState].
extension WordScrambleGameStatePatterns on WordScrambleGameState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WordScrambleGameState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WordScrambleGameState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WordScrambleGameState value)  $default,){
final _that = this;
switch (_that) {
case _WordScrambleGameState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WordScrambleGameState value)?  $default,){
final _that = this;
switch (_that) {
case _WordScrambleGameState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<String>? wordList,  int? currentWordIndex,  WordScrambleGame? currentGame,  int? totalScore,  bool isGameCompleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WordScrambleGameState() when $default != null:
return $default(_that.wordList,_that.currentWordIndex,_that.currentGame,_that.totalScore,_that.isGameCompleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<String>? wordList,  int? currentWordIndex,  WordScrambleGame? currentGame,  int? totalScore,  bool isGameCompleted)  $default,) {final _that = this;
switch (_that) {
case _WordScrambleGameState():
return $default(_that.wordList,_that.currentWordIndex,_that.currentGame,_that.totalScore,_that.isGameCompleted);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<String>? wordList,  int? currentWordIndex,  WordScrambleGame? currentGame,  int? totalScore,  bool isGameCompleted)?  $default,) {final _that = this;
switch (_that) {
case _WordScrambleGameState() when $default != null:
return $default(_that.wordList,_that.currentWordIndex,_that.currentGame,_that.totalScore,_that.isGameCompleted);case _:
  return null;

}
}

}

/// @nodoc


class _WordScrambleGameState implements WordScrambleGameState {
   _WordScrambleGameState({final  List<String>? wordList, this.currentWordIndex, this.currentGame, this.totalScore, this.isGameCompleted = false}): _wordList = wordList;
  

 final  List<String>? _wordList;
@override List<String>? get wordList {
  final value = _wordList;
  if (value == null) return null;
  if (_wordList is EqualUnmodifiableListView) return _wordList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? currentWordIndex;
@override final  WordScrambleGame? currentGame;
@override final  int? totalScore;
@override@JsonKey() final  bool isGameCompleted;

/// Create a copy of WordScrambleGameState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WordScrambleGameStateCopyWith<_WordScrambleGameState> get copyWith => __$WordScrambleGameStateCopyWithImpl<_WordScrambleGameState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WordScrambleGameState&&const DeepCollectionEquality().equals(other._wordList, _wordList)&&(identical(other.currentWordIndex, currentWordIndex) || other.currentWordIndex == currentWordIndex)&&(identical(other.currentGame, currentGame) || other.currentGame == currentGame)&&(identical(other.totalScore, totalScore) || other.totalScore == totalScore)&&(identical(other.isGameCompleted, isGameCompleted) || other.isGameCompleted == isGameCompleted));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_wordList),currentWordIndex,currentGame,totalScore,isGameCompleted);

@override
String toString() {
  return 'WordScrambleGameState(wordList: $wordList, currentWordIndex: $currentWordIndex, currentGame: $currentGame, totalScore: $totalScore, isGameCompleted: $isGameCompleted)';
}


}

/// @nodoc
abstract mixin class _$WordScrambleGameStateCopyWith<$Res> implements $WordScrambleGameStateCopyWith<$Res> {
  factory _$WordScrambleGameStateCopyWith(_WordScrambleGameState value, $Res Function(_WordScrambleGameState) _then) = __$WordScrambleGameStateCopyWithImpl;
@override @useResult
$Res call({
 List<String>? wordList, int? currentWordIndex, WordScrambleGame? currentGame, int? totalScore, bool isGameCompleted
});


@override $WordScrambleGameCopyWith<$Res>? get currentGame;

}
/// @nodoc
class __$WordScrambleGameStateCopyWithImpl<$Res>
    implements _$WordScrambleGameStateCopyWith<$Res> {
  __$WordScrambleGameStateCopyWithImpl(this._self, this._then);

  final _WordScrambleGameState _self;
  final $Res Function(_WordScrambleGameState) _then;

/// Create a copy of WordScrambleGameState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? wordList = freezed,Object? currentWordIndex = freezed,Object? currentGame = freezed,Object? totalScore = freezed,Object? isGameCompleted = null,}) {
  return _then(_WordScrambleGameState(
wordList: freezed == wordList ? _self._wordList : wordList // ignore: cast_nullable_to_non_nullable
as List<String>?,currentWordIndex: freezed == currentWordIndex ? _self.currentWordIndex : currentWordIndex // ignore: cast_nullable_to_non_nullable
as int?,currentGame: freezed == currentGame ? _self.currentGame : currentGame // ignore: cast_nullable_to_non_nullable
as WordScrambleGame?,totalScore: freezed == totalScore ? _self.totalScore : totalScore // ignore: cast_nullable_to_non_nullable
as int?,isGameCompleted: null == isGameCompleted ? _self.isGameCompleted : isGameCompleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of WordScrambleGameState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WordScrambleGameCopyWith<$Res>? get currentGame {
    if (_self.currentGame == null) {
    return null;
  }

  return $WordScrambleGameCopyWith<$Res>(_self.currentGame!, (value) {
    return _then(_self.copyWith(currentGame: value));
  });
}
}

// dart format on

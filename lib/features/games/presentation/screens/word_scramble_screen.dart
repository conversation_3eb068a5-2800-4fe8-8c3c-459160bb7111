import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/games/presentation/providers/state/word_scramble_state.dart';
import 'package:selfeng/features/games/presentation/providers/word_scramble_controller.dart';
import 'package:selfeng/features/games/presentation/widgets/loading_screen.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';

class WordScrambleScreen extends ConsumerStatefulWidget {
  const WordScrambleScreen({super.key, required this.topic});
  final String topic;

  @override
  ConsumerState<WordScrambleScreen> createState() => _WordScrambleScreenState();
}

class _WordScrambleScreenState extends ConsumerState<WordScrambleScreen>
    with TickerProviderStateMixin {
  late AsyncValue<WordScrambleGameState> viewState;
  late WordScrambleController viewModel;
  late AsyncValue viewTimerState;
  late TimerController viewTimerModel;

  @override
  Widget build(BuildContext context) {
    _initializeProviders();

    return switch (viewState) {
      AsyncData() => _buildBody(),
      AsyncError() => _buildBody(),
      AsyncLoading() => LoadingScreen(),
      _ => const Text('loading'),
    };
  }

  void _initializeProviders() {
    final prov = wordScrambleControllerProvider(topic: widget.topic);
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final provTimer = timerControllerProvider;
    viewTimerState = ref.watch(provTimer);
    viewTimerModel = ref.watch(provTimer.notifier);
  }

  Widget _buildBody() {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildScoreSection(),
            // const SizedBox(height: 20),
            _buildGameArea(),
            // const SizedBox(height: 20),
            _buildControlButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Word: ${viewState.value?.currentWordIndex ?? 0 + 1}/${viewState.value?.wordList?.length ?? 0}',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Text(
            'Score: ${viewState.value?.totalScore ?? 0}',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Selected letters area (answer area)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.3),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Text(
                    'Arrange the letters to form a word',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),
                  _buildSelectedLetters(),
                ],
              ),
            ),
            const SizedBox(height: 40),
            // Scrambled letters area (options area)
            _buildScrambledLetters(),

            // Show result when word is completed
            if (viewState.value!.currentGame!.isCompleted)
              _buildCompletionMessage(),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedLetters() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      alignment: WrapAlignment.center,
      children: List.generate(
        viewState.value!.currentGame!.selectedLetters!.length,
        (index) => _buildLetterTile(
          viewState.value!.currentGame!.selectedLetters![index],
          () => viewModel.unselectLetter(index),
          Colors.blue.shade100,
        ),
      ),
    );
  }

  Widget _buildScrambledLetters() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      alignment: WrapAlignment.center,
      children: List.generate(
        viewState.value!.currentGame!.scrambledLetters!.length,
        (index) => _buildLetterTile(
          viewState.value!.currentGame!.scrambledLetters![index],
          () => viewModel.selectLetter(index),
          Colors.amber.shade100,
        ),
      ),
    );
  }

  Widget _buildLetterTile(String letter, VoidCallback onTap, Color color) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 50,
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Text(
          letter,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildCompletionMessage() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Text(
            'Correct!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '+${viewState.value!.currentGame!.score!} points',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: viewModel.resetGame,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'Reset',
              style: TextStyle(fontSize: 16, color: Colors.white),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              viewModel.exitGame();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'Exit',
              style: TextStyle(fontSize: 16, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}

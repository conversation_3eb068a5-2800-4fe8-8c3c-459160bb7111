import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart' show customNav;

class GamesScreen extends StatelessWidget {
  const GamesScreen({super.key});

  // The _buildGameCard method is removed as its logic is now in _GameCard widget.

  @override
  Widget build(BuildContext context) {
    const games = [
      {
        'image': 'assets/images/games/wordscramble.png',
        'title': "Word\nScramble 🔠 🔗",
        'route': RouterName.wordScramble,
      },
      {
        'image': 'assets/images/games/sentencefix.png',
        'title': "Sentence\nFix 🔤 ✅",
        'route': RouterName.memoryFlash, // TODO: Update with actual routes
      },
      {
        'image': 'assets/images/games/memoryflash.png',
        'title': 'Memory\nFlash ⚡🧠',
        'route': RouterName.memoryFlash,
      },
      {
        'image': 'assets/images/games/conversationbuilder.png',
        'title': 'Conversation\nBuilder 🗣️ 🧩',
        'route': RouterName.memoryFlash,
      },
    ];
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Word Master Challenge 🎯 🎮',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.separated(
                  padding: const EdgeInsets.only(bottom: 16),
                  itemCount: games.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (context, index) {
                    // Use the new _GameCard widget
                    return _GameCard(
                      imagePath: games[index]['image']!,
                      gameTitle: games[index]['title']!,
                      route: games[index]['route']!,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _GameCard extends StatefulWidget {
  final String imagePath;
  final String gameTitle;
  final String route;

  const _GameCard({
    required this.imagePath,
    required this.gameTitle,
    required this.route,
  });

  @override
  State<_GameCard> createState() => _GameCardState();
}

class _GameCardState extends State<_GameCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isProcessingTap = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100), // Duration for scale down
      reverseDuration: const Duration(
        milliseconds: 100,
      ), // Duration for scale up
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (_isProcessingTap) return; // If a tap is already being processed, ignore
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) async {
    if (_isProcessingTap) {
      return; // Already processing a tap that will lead to navigation
    }

    // Check if the card was actually pressed (animation started or completed)
    if (_controller.status == AnimationStatus.forward ||
        _controller.value > 0.0) {
      _isProcessingTap = true; // Mark as processing
      await _controller.reverse(); // Animate back to original size
      if (mounted) {
        // Check if the widget is still in the tree
        customNav(
          context,
          RouterName.topicGame,
          params: {"game": widget.route},
        );
        // Assuming navigation will unmount this widget or make it non-interactive.
        // If it could remain interactive, _isProcessingTap should be reset, e.g., after a delay.
        // For typical page navigation, this reset is often not needed here.
      } else {
        _isProcessingTap =
            false; // Reset if widget got disposed during animation
      }
    }
  }

  void _handleTapCancel() {
    // If a tap is cancelled, animate back to original size if it was scaled down
    if (_controller.status == AnimationStatus.forward ||
        _controller.value > 0.0) {
      _controller.reverse();
    }
    _isProcessingTap = false; // Reset processing flag as navigation won't occur
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  widget.imagePath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red,
                      ),
                    );
                  },
                ),
              ),
              Positioned(
                left: 14,
                bottom: 26,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.gameTitle,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        'Play Now',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<ObserveRecalRoute>(
  path: '/memory-flash/:topic',
  name: RouterName.memoryFlash,
)
class ObserveRecalRoute extends GoRouteData with $ObserveRecalRoute {
  const ObserveRecalRoute(this.topic);
  final String topic;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      memory_flash.loadLibrary,
      () => memory_flash.MemoryFlashScreen(topic: topic),
    );
  }
}

@TypedGoRoute<TopicGameRoute>(
  path: '/topic-game/:game',
  name: RouterName.topicGame,
)
class TopicGameRoute extends GoRouteData with $TopicGameRoute {
  const TopicGameRoute(this.game);
  final String game;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      topic_game.loadLibrary,
      () => topic_game.TopicGameScreen(game: game),
    );
  }
}

@TypedGoRoute<WordScrambleRoute>(
  path: '/word-scramble/:topic',
  name: RouterName.wordScramble,
)
class WordScrambleRoute extends GoRouteData with $WordScrambleRoute {
  const WordScrambleRoute(this.topic);
  final String topic;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      word_scramble.loadLibrary,
      () => word_scramble.WordScrambleScreen(topic: topic),
    );
  }
}

import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/setting/domain/models/language.dart';
import 'package:selfeng/shared/globals.dart';

part 'setting_state.freezed.dart';

@freezed
abstract class SettingState with _$SettingState {
  const factory SettingState({
    @Default(Locale('en', 'US')) Locale locale,
    @Default(1) int selectedIndex,
    @Default([]) List<String> isNewUser,
  }) = _SettingState;

  String get getLanguageCode => locale.languageCode;

  List<Locale> get localeSupport => [
    const Locale('en', 'US'),
    const Locale('id', 'IDN'),
  ];

  LanguageList get languageList => [
    Language(
      title: "Bahasa Indonesia",
      icon: '$assetImageLanguage/ID-IDN-rounded.png',
      imageBackground: '$assetImageLanguage/Bedugul.png',
      localValue: const Locale('id', 'IDN'),
    ),
    Language(
      title: "English",
      icon: '$assetImageLanguage/ENG-US-rounded.png',
      imageBackground: '$assetImageLanguage/Liberty.png',
      localValue: const Locale('en', 'US'),
    ),
  ];
  // Allow custom getters / setters
  const SettingState._();
}

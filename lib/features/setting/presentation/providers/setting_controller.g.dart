// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles our authentication state

@ProviderFor(SettingController)
const settingControllerProvider = SettingControllerProvider._();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
final class SettingControllerProvider
    extends $AsyncNotifierProvider<SettingController, SettingState> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  const SettingControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'settingControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$settingControllerHash();

  @$internal
  @override
  SettingController create() => SettingController();
}

String _$settingControllerHash() => r'b3de08409783df905f40c4450ee8cdc173ba8bd1';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state

abstract class _$SettingController extends $AsyncNotifier<SettingState> {
  FutureOr<SettingState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<SettingState>, SettingState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<SettingState>, SettingState>,
              AsyncValue<SettingState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

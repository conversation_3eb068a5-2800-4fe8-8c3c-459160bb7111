// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_material_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(LearningMaterialController)
const learningMaterialControllerProvider =
    LearningMaterialControllerProvider._();

final class LearningMaterialControllerProvider
    extends
        $AsyncNotifierProvider<
          LearningMaterialController,
          LearningMaterialState
        > {
  const LearningMaterialControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'learningMaterialControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$learningMaterialControllerHash();

  @$internal
  @override
  LearningMaterialController create() => LearningMaterialController();
}

String _$learningMaterialControllerHash() =>
    r'24d35477bbd0806815465acfe5ba26af601299c1';

abstract class _$LearningMaterialController
    extends $AsyncNotifier<LearningMaterialState> {
  FutureOr<LearningMaterialState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<AsyncValue<LearningMaterialState>, LearningMaterialState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<LearningMaterialState>,
                LearningMaterialState
              >,
              AsyncValue<LearningMaterialState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

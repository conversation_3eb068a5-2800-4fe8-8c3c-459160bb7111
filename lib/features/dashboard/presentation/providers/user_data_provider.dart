import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

final userDataProvider = FutureProvider<UserData>((ref) async {
  final repository = ref.watch(userDataServiceProvider);
  final result = await repository.getUserData();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (userData) => userData,
  );
});

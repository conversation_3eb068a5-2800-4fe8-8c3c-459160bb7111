// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tab_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(TabController)
const tabControllerProvider = TabControllerProvider._();

final class TabControllerProvider
    extends $NotifierProvider<TabController, int> {
  const TabControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'tabControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$tabControllerHash();

  @$internal
  @override
  TabController create() => TabController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$tabControllerHash() => r'89fbffd2772bd75d3f0719c11d96547e253c00de';

abstract class _$TabController extends $Notifier<int> {
  int build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<int, int>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<int, int>,
              int,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

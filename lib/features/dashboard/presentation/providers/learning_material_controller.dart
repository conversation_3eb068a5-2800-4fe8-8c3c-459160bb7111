import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/dashboard/presentation/providers/state/learning_material_state.dart';
import 'package:selfeng/features/dashboard/presentation/providers/user_data_provider.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'learning_material_controller.g.dart';

@riverpod
class LearningMaterialController extends _$LearningMaterialController {
  late final MainLessonRepository mainLessonRepository;

  @override
  FutureOr<LearningMaterialState> build() {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _loadChapters();
    return LearningMaterialState();
  }

  Future<void> _loadChapters() async {
    state = const AsyncLoading();

    // Set Crashlytics context
    final crashlytics = ref.read(crashlyticsServiceProvider);
    await crashlytics.setCustomKeys({
      'feature': 'learning_material',
      'controller': 'learning_material_controller',
    });

    try {
      final userData = await ref.read(userDataProvider.future);
      final fetchedLevel = _determineFetchedLevel(userData);
      final chapters = await mainLessonRepository.getChapters(fetchedLevel);

      chapters.fold(
        (failure) {
          crashlytics.recordError(
            Exception(failure.message),
            StackTrace.empty,
            reason: 'Failed to load chapters',
            context: {
              'fetched_level': fetchedLevel.toString(),
              'feature': 'learning_material',
            },
          );
          state = AsyncError(failure.message, StackTrace.empty);
        },
        (data) => state = AsyncData(
          LearningMaterialState(chapters: data, fetchedLevel: fetchedLevel),
        ),
      );
    } catch (e, st) {
      crashlytics.recordError(
        e,
        st,
        reason: 'Failed to load learning materials',
        context: {'feature': 'learning_material'},
      );
      state = AsyncError(e.toString(), st);
    }
  }

  Level _determineFetchedLevel(UserData userData) {
    // Extract raw last courses
    final rawLastCourses = [
      userData.lastPronunciation,
      userData.lastConversation,
      userData.lastListening,
      userData.lastSpeaking,
    ].whereType<LastCourse>().toList();

    // Default level is A1
    if (rawLastCourses.isEmpty) {
      return Level.a1;
    }

    // Group the data by level
    final Map<Level, int> levelCounts = {};

    final crashlytics = ref.read(crashlyticsServiceProvider);

    for (final course in rawLastCourses) {
      try {
        final level = Level.values.byName(course.level.toLowerCase());
        levelCounts[level] = (levelCounts[level] ?? 0) + 1;
      } catch (e, stackTrace) {
        crashlytics.recordError(
          e,
          stackTrace,
          reason: 'Invalid level in course info',
          context: {
            'invalid_level': course.level,
            'feature': 'learning_material',
            'operation': 'determine_fetched_level',
          },
        );
        // Skip entries with invalid level strings
      }
    }

    if (levelCounts.isEmpty) {
      return Level.a1;
    }

    // Find the maximum count first
    final maxCount = levelCounts.values.reduce((a, b) => a > b ? a : b);

    // Get all levels with the maximum count
    final levelsWithMaxCount = levelCounts.entries
        .where((entry) => entry.value == maxCount)
        .map((entry) => entry.key)
        .toList();

    // If there's only one level with max count, return it
    if (levelsWithMaxCount.length == 1) {
      return levelsWithMaxCount.first;
    }

    // If there are multiple levels with the same max count, return the lowest level
    // Level order: A1, A2, B1, B2, C1, C2 (by index)
    levelsWithMaxCount.sort((a, b) => a.index.compareTo(b.index));
    return levelsWithMaxCount.first;
  }

  void changeisFromLastCourse(bool fromLastCourse) {
    final mainLessonStateNotifier = ref.read(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateFromLastCourse(fromLastCourse);
  }
}

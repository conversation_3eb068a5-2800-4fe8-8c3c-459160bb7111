import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/presentation/providers/user_data_provider.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

final lastCourseProvider = FutureProvider<List<LastCourseInfo>>((ref) async {
  final userData = await ref.watch(userDataProvider.future);
  final mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
  final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
  final crashlytics = ref.read(crashlyticsServiceProvider);

  final List<LastCourseInfo> lastCourse = [];
  final futures = <Future<LastCourseInfo?>>[];
  final coursesToProcess = [
    userData.lastPronunciation,
    userData.lastConversation,
    userData.lastListening,
    userData.lastSpeaking,
  ];

  for (final course in coursesToProcess.whereType<LastCourse>()) {
    futures.add(_getLastCourseData(course, mainLessonRepository, crashlytics));
  }

  final results = await Future.wait(futures);
  lastCourse.addAll(results.whereType<LastCourseInfo>());

  for (final res in lastCourse) {
    _updateMainLessonState(res.info, mainLessonStateNotifier);
  }

  lastCourse.sort((a, b) => b.info.accessTime.compareTo(a.info.accessTime));
  return lastCourse;
});

Future<LastCourseInfo?> _getLastCourseData(
  LastCourse lastCourse,
  MainLessonRepository repository,
  dynamic crashlytics,
) async {
  try {
    final result = await switch (lastCourse.section) {
      SectionType.conversation => repository.getConversation(
        path: lastCourse.path,
      ),
      SectionType.listening => repository.getListening(path: lastCourse.path),
      SectionType.speaking => repository.getSpeaking(path: lastCourse.path),
      SectionType.pronunciation => repository.getPronunciation(
        path: lastCourse.path,
      ),
    };

    return result.fold((failure) {
      crashlytics.recordError(
        Exception(failure.message),
        StackTrace.empty,
        reason: 'Get Last Course Data failed',
      );
      return null;
    }, (data) => LastCourseInfo(info: lastCourse, data: data));
  } catch (e, st) {
    crashlytics.recordError(e, st, reason: 'Get Last Course Data error');
    return null;
  }
}

void _updateMainLessonState(LastCourse info, MainLessonStateNotifier notifier) {
  switch (info.section) {
    case SectionType.pronunciation:
      notifier.updateLastPronunciation(info);
      break;
    case SectionType.conversation:
      notifier.updateLastConversation(info);
      break;
    case SectionType.listening:
      notifier.updateLastListening(info);
      break;
    case SectionType.speaking:
      notifier.updateLastSpeaking(info);
      break;
  }
}

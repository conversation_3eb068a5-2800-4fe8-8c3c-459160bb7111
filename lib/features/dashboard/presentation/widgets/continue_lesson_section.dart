import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/presentation/providers/last_course_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/last_lesson_card_fix.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class ContinueLessonsSection extends ConsumerWidget {
  const ContinueLessonsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lastCourseAsync = ref.watch(lastCourseProvider);
    final mainLessonStateNotifier = ref.read(mainLessonStateProvider.notifier);

    return lastCourseAsync.when(
      data: (courses) => courses.isEmpty
          ? const SizedBox.shrink()
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    context.loc.continueYourLessons,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 6),
                Container(
                  width: double.infinity,
                  height: 220,
                  padding: const EdgeInsets.symmetric(vertical: 18.0),
                  decoration: BoxDecoration(
                    color: Color(0xffFFEDEB),
                    border: Border.all(color: Color(0xffFFB3AC)),
                  ),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _prepareLastLessonCards(
                        ({
                          required lastCourse,
                          required totalCourses,
                          required onTap,
                        }) => LastLessonCardFix(
                          lastCourse: lastCourse,
                          totalCourses: totalCourses,
                          onTap: onTap,
                        ),
                        courses,
                        mainLessonStateNotifier,
                      ),
                    ),
                  ),
                ),
              ],
            ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  List<Widget> _prepareLastLessonCards(
    Widget Function({
      required dynamic lastCourse,
      required int totalCourses,
      required VoidCallback onTap,
    })
    cardBuilder,
    List<LastCourseInfo> courses,
    MainLessonStateNotifier notifier,
  ) {
    if (courses.isEmpty) return [];

    final totalCourse = courses.length;
    return courses
        .map(
          (data) => cardBuilder(
            lastCourse: data,
            totalCourses: totalCourse,
            onTap: () => notifier.updateFromLastCourse(true),
          ),
        )
        .toList();
  }
}

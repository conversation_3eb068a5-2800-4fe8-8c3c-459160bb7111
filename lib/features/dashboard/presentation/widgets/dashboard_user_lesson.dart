import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/presentation/providers/user_data_provider.dart';
import 'package:selfeng/features/dashboard/presentation/providers/last_course_provider.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/assessment_card.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/continue_lesson_section.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/get_started_section.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class DashboardUserLesson extends ConsumerStatefulWidget {
  const DashboardUserLesson({super.key});

  @override
  ConsumerState<DashboardUserLesson> createState() =>
      _DashboardUserLessonState();
}

class _DashboardUserLessonState extends ConsumerState<DashboardUserLesson> {
  @override
  Widget build(BuildContext context) {
    final userDataAsync = ref.watch(userDataProvider);
    final lastCourseAsync = ref.watch(lastCourseProvider);

    return userDataAsync.when(
      data: (userData) => lastCourseAsync.when(
        data: (lastCourse) {
          final bool showAssessment = !userData.afterTest;
          final bool hasCourse = lastCourse.isNotEmpty;

          return Column(
            children: [
              if (showAssessment) const AssessmentCard(),
              if (hasCourse)
                const ContinueLessonsSection()
              else
                const GetStartedSection(),
            ],
          );
        },
        loading: () => const LoadingCircle(),
        error: (error, stackTrace) => Center(
          child: Text(
            error.toString(),
            style: const TextStyle(color: Colors.red),
          ),
        ),
      ),
      loading: () => const LoadingCircle(),
      error: (error, stackTrace) => Center(
        child: Text(
          error.toString(),
          style: const TextStyle(color: Colors.red),
        ),
      ),
    );
  }
}

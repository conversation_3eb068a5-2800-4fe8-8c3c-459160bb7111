// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timer_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// A Riverpod controller that manages timer functionality with state management

@ProviderFor(TimerController)
const timerControllerProvider = TimerControllerProvider._();

/// A Riverpod controller that manages timer functionality with state management
final class TimerControllerProvider
    extends $AsyncNotifierProvider<TimerController, TimerState> {
  /// A Riverpod controller that manages timer functionality with state management
  const TimerControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'timerControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$timerControllerHash();

  @$internal
  @override
  TimerController create() => TimerController();
}

String _$timerControllerHash() => r'a0ebdd15a0ecc3b91aa728cbeda3e855b40e4c32';

/// A Riverpod controller that manages timer functionality with state management

abstract class _$TimerController extends $AsyncNotifier<TimerState> {
  FutureOr<TimerState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<TimerState>, TimerState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<TimerState>, TimerState>,
              AsyncValue<TimerState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

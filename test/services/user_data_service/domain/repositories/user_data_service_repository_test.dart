import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import '../../../../mocks/firebase_mocks.dart';

// Additional mock classes for this test
class MockFirebaseFunctions extends Mock implements FirebaseFunctions {}

class MockHttpsCallable extends Mock implements HttpsCallable {}

class MockHttpsCallableResult extends Mock implements HttpsCallableResult {}

// Firestore wrapper backed by FakeFirebaseFirestore so we avoid mocking sealed SDK classes
class FakeFirestoreServiceRepository implements FirestoreServiceRepository {
  FakeFirestoreServiceRepository(
    this.db,
    this._auth,
    this._storage,
    this._functions,
  );
  final FirebaseFirestore db;
  final FirebaseAuth _auth;
  final FirebaseStorage _storage;
  final FirebaseFunctions _functions;

  @override
  DocumentReference<Map<String, dynamic>> dataUser() =>
      db.collection('user-data').doc(_auth.currentUser?.uid);

  @override
  FirebaseFirestore get fireStore => db;

  @override
  FirebaseStorage get firebaseStorage => _storage;

  @override
  FirebaseAuth get firebaseAuth => _auth;

  @override
  FirebaseFunctions get firebaseFunctions => _functions;
}

void main() {
  group('UserDataServiceRepository', () {
    late UserDataServiceRepository repository;
    late FakeFirebaseFirestore fakeDb;
    late MockFirebaseAuth mockAuth;
    late MockFirebaseStorage mockStorage;
    late MockFirebaseFunctions mockFirebaseFunctions;
    late MockHttpsCallable mockHttpsCallable;

    setUp(() {
      fakeDb = FakeFirebaseFirestore();
      mockAuth = MockFirebaseAuth();
      mockStorage = MockFirebaseStorage();
      mockFirebaseFunctions = MockFirebaseFunctions();
      mockHttpsCallable = MockHttpsCallable();

      repository = UserDataServiceRepository(
        FakeFirestoreServiceRepository(
          fakeDb,
          mockAuth,
          mockStorage,
          mockFirebaseFunctions,
        ),
      );

      // Register fallback values for mocktail
      registerFallbackValue(<String, dynamic>{});
      registerFallbackValue(SetOptions(merge: true));
      registerFallbackValue(FieldValue.serverTimestamp());
    });

    group('getUserData', () {
      test('should return user data when document exists', () async {
        // Arrange
        final userData = UserData(email: '<EMAIL>', afterTest: true);
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        // Set the user data in the fake database
        await fakeDb.collection('user-data').doc('u1').set(userData.toJson());

        when(
          () => mockFirebaseFunctions.httpsCallable('reinitUserData'),
        ).thenReturn(mockHttpsCallable);
        when(
          () => mockHttpsCallable.call(),
        ).thenAnswer((_) async => MockHttpsCallableResult());

        // Act
        final result = await repository.getUserData();

        // Assert
        expect(result, isA<Right<AppException, UserData>>());
        expect(
          (result as Right<AppException, UserData>).value.email,
          equals('<EMAIL>'),
        );
      });

      test(
        'should return Left with AppException when getting user data fails',
        () async {
          // Arrange: simulate reinitUserData failure
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);
          when(
            () => mockFirebaseFunctions.httpsCallable('reinitUserData'),
          ).thenReturn(mockHttpsCallable);
          when(
            () => mockHttpsCallable.call(),
          ).thenThrow(Exception('Failed to get user data'));

          // Act
          final result = await repository.getUserData();

          // Assert
          expect(result, isA<Left<AppException, UserData>>());
          expect(
            (result as Left<AppException, UserData>).value.identifier,
            equals('Failed fetch user data'),
          );
        },
      );
    });

    group('updateLastCourse', () {
      test('should update last course for pronunciation section', () async {
        // Arrange
        final lastCourse = LastCourse(
          accessTime: DateTime(2023, 1, 1),
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: '/path/to/pronunciation',
        );
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);
        await fakeDb.collection('user-data').doc('u1').set({});

        // Act
        final result = await repository.updateLastCourse(
          lastCourse: lastCourse,
          section: SectionType.pronunciation,
        );

        // Assert
        expect(result, isA<Right<AppException, LastCourse>>());
        final snap = await fakeDb.collection('user-data').doc('u1').get();
        expect(
          (snap.data()!['last_pronunciation'] as Map)['path'],
          '/path/to/pronunciation',
        );
      });

      test(
        'should return Left with AppException when updating last course fails',
        () async {
          // Arrange: no user
          when(() => mockAuth.currentUser).thenReturn(null);

          // Act
          final result = await repository.updateLastCourse(
            lastCourse: LastCourse(
              accessTime: DateTime(2023, 1, 1),
              level: 'A1',
              chapter: 1,
              section: SectionType.pronunciation,
              path: '/path',
            ),
            section: SectionType.pronunciation,
          );

          // Assert
          expect(result, isA<Left<AppException, LastCourse>>());
        },
      );
    });

    group('saveLessonResult', () {
      test('should save lesson result successfully', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);
        await fakeDb.collection('user-data').doc('u1').set({});

        final lessonResult = LessonResult(
          contentOrder: 1,
          path: '/path/to/content',
          result: <String, dynamic>{},
        );

        // Act
        final res = await repository.saveLessonResult(
          level: 'A1',
          chapter: '1',
          section: SectionType.pronunciation,
          result: lessonResult,
        );

        // Assert
        expect(res, isA<Right<AppException, dynamic>>());
        final resultsColl =
            await fakeDb
                .collection('user-data')
                .doc('u1')
                .collection('lessons')
                .doc('A1')
                .collection('chapters')
                .doc('1')
                .collection('sections')
                .doc('pronunciation')
                .collection('results')
                .get();
        expect(resultsColl.docs.length, 1);
      });

      test(
        'should save lesson result successfully even when user is not authenticated',
        () async {
          // Arrange: no user (should still work with generated document ID)
          when(() => mockAuth.currentUser).thenReturn(null);
          final lessonResult = LessonResult(
            contentOrder: 1,
            path: '/path/to/content',
            result: <String, dynamic>{},
          );

          // Act
          final res = await repository.saveLessonResult(
            level: 'A1',
            chapter: '1',
            section: SectionType.pronunciation,
            result: lessonResult,
          );

          // Assert
          expect(res, isA<Right<AppException, dynamic>>());
        },
      );
    });

    group('getPronunciationResult', () {
      test(
        'should return pronunciation results when documents exist',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          final lessonResult = LessonResult(
            contentOrder: 1,
            path: '/path/to/content',
            result: <String, dynamic>{'score': 85.0},
          );

          // Set up data in fake database
          await fakeDb
              .collection('user-data')
              .doc('u1')
              .collection('lessons')
              .doc('A1')
              .collection('chapters')
              .doc('1')
              .collection('sections')
              .doc('pronunciation')
              .collection('results')
              .doc('result1')
              .set(lessonResult.toJson());

          final params = PronunciationScoreParams(level: 'A1', chapter: '1');

          // Act
          final result = await repository.getPronunciationResult(params);

          // Assert
          expect(result, isA<Right<AppException, List<LessonResult>>>());
          expect(
            (result as Right<AppException, List<LessonResult>>).value.length,
            1,
          );
          expect((result).value.first.path, '/path/to/content');
        },
      );

      test('should return empty list when no documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        // Act
        final result = await repository.getPronunciationResult(params);

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value,
          isEmpty,
        );
      });

      test('should filter results by partOrder when specified', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final lessonResult1 = LessonResult(
          partOrder: 1,
          contentOrder: 1,
          path: '/path/to/content1',
          result: <String, dynamic>{'score': 85.0},
        );

        final lessonResult2 = LessonResult(
          partOrder: 2,
          contentOrder: 1,
          path: '/path/to/content2',
          result: <String, dynamic>{'score': 90.0},
        );

        // Set up data in fake database
        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('pronunciation')
            .collection('results')
            .doc('result1')
            .set(lessonResult1.toJson());

        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('pronunciation')
            .collection('results')
            .doc('result2')
            .set(lessonResult2.toJson());

        final params = PronunciationScoreParams(
          level: 'A1',
          chapter: '1',
          partOrder: 1,
        );

        // Act
        final result = await repository.getPronunciationResult(params);

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value.length,
          1,
        );
        expect((result).value.first.partOrder, 1);
      });

      test(
        'should return Left with AppException when getting results fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          final params = PronunciationScoreParams(level: 'A1', chapter: '1');

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.getPronunciationResult(params);

          // Assert
          expect(result, isA<Left<AppException, List<LessonResult>>>());
          expect(
            (result as Left<AppException, List<LessonResult>>).value.identifier,
            'Failed get pronunciation result',
          );
        },
      );
    });

    group('calculatePronunciationResult', () {
      test(
        'should calculate pronunciation aggregate scores successfully',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          final lessonResult1 = LessonResult(
            contentOrder: 1,
            path: '/path/to/content1',
            result: <String, dynamic>{
              'accuracyScore': 80.0,
              'fluencyScore': 75.0,
              'prosodyScore': 85.0,
              'completenessScore': 90.0,
              'pronScore': 82.5,
            },
          );

          final lessonResult2 = LessonResult(
            contentOrder: 2,
            path: '/path/to/content2',
            result: <String, dynamic>{
              'accuracyScore': 85.0,
              'fluencyScore': 80.0,
              'prosodyScore': 88.0,
              'completenessScore': 92.0,
              'pronScore': 86.25,
            },
          );

          // Set up data in fake database
          await fakeDb
              .collection('user-data')
              .doc('u1')
              .collection('lessons')
              .doc('A1')
              .collection('chapters')
              .doc('1')
              .collection('sections')
              .doc('pronunciation')
              .collection('results')
              .doc('result1')
              .set(lessonResult1.toJson());

          await fakeDb
              .collection('user-data')
              .doc('u1')
              .collection('lessons')
              .doc('A1')
              .collection('chapters')
              .doc('1')
              .collection('sections')
              .doc('pronunciation')
              .collection('results')
              .doc('result2')
              .set(lessonResult2.toJson());

          final params = PronunciationScoreParams(level: 'A1', chapter: '1');

          // Act
          final result = await repository.calculatePronunciationResult(params);

          // Assert
          expect(
            result,
            isA<Right<AppException, PronunciationAgregateScore>>(),
          );
          final aggregateScore =
              (result as Right<AppException, PronunciationAgregateScore>).value;
          expect(aggregateScore.dataCount, 2);
          expect(aggregateScore.accuracyScore, 165.0); // 80.0 + 85.0
          expect(aggregateScore.fluencyScore, 155.0); // 75.0 + 80.0
          expect(aggregateScore.prosodyScore, 173.0); // 85.0 + 88.0
          expect(aggregateScore.completenessScore, 182.0); // 90.0 + 92.0
          expect(aggregateScore.pronScore, 168.75); // 82.5 + 86.25
        },
      );

      test('should return zero scores when no results exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        // Act
        final result = await repository.calculatePronunciationResult(params);

        // Assert
        expect(result, isA<Right<AppException, PronunciationAgregateScore>>());
        final aggregateScore =
            (result as Right<AppException, PronunciationAgregateScore>).value;
        expect(aggregateScore.dataCount, 0);
        expect(aggregateScore.accuracyScore, 0.0);
        expect(aggregateScore.fluencyScore, 0.0);
        expect(aggregateScore.prosodyScore, 0.0);
        expect(aggregateScore.completenessScore, 0.0);
        expect(aggregateScore.pronScore, 0.0);
      });

      test(
        'should return Left with AppException when calculation fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          final params = PronunciationScoreParams(level: 'A1', chapter: '1');

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.calculatePronunciationResult(params);

          // Assert
          expect(result, isA<Left<AppException, PronunciationAgregateScore>>());
          expect(
            (result as Left<AppException, PronunciationAgregateScore>)
                .value
                .identifier,
            'Failed get pronunciation result',
          );
        },
      );
    });

    group('getConversationResult', () {
      test('should return conversation results when documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final lessonResult = LessonResult(
          contentOrder: 1,
          path: '/path/to/conversation',
          result: <String, dynamic>{'score': 85.0},
        );

        // Set up data in fake database
        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('conversation')
            .collection('results')
            .doc('result1')
            .set(lessonResult.toJson());

        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        // Act
        final result = await repository.getConversationResult(params);

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value.length,
          1,
        );
        expect((result).value.first.path, '/path/to/conversation');
      });

      test('should return empty list when no documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        // Act
        final result = await repository.getConversationResult(params);

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value,
          isEmpty,
        );
      });

      test(
        'should return Left with AppException when getting results fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          final params = PronunciationScoreParams(level: 'A1', chapter: '1');

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.getConversationResult(params);

          // Assert
          expect(result, isA<Left<AppException, List<LessonResult>>>());
          expect(
            (result as Left<AppException, List<LessonResult>>).value.identifier,
            'Failed get conversation result',
          );
        },
      );
    });

    group('getListeningResult', () {
      test('should return listening results when documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final lessonResult = LessonResult(
          contentOrder: 1,
          path: '/path/to/listening',
          result: <String, dynamic>{'score': 85.0},
        );

        // Set up data in fake database
        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('listening')
            .collection('results')
            .doc('result1')
            .set(lessonResult.toJson());

        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        // Act
        final result = await repository.getListeningResult(params);

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value.length,
          1,
        );
        expect((result).value.first.path, '/path/to/listening');
      });

      test('should return empty list when no documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        // Act
        final result = await repository.getListeningResult(params);

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value,
          isEmpty,
        );
      });

      test(
        'should return Left with AppException when getting results fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          final params = PronunciationScoreParams(level: 'A1', chapter: '1');

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.getListeningResult(params);

          // Assert
          expect(result, isA<Left<AppException, List<LessonResult>>>());
          expect(
            (result as Left<AppException, List<LessonResult>>).value.identifier,
            'Failed get listening result',
          );
        },
      );
    });

    group('getSpeakingResult', () {
      test('should return speaking results when documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final lessonResult = LessonResult(
          contentOrder: 1,
          path: '/path/to/speaking',
          result: <String, dynamic>{'score': 85.0},
          speakingStage: SpeakingStage.stage1,
        );

        // Set up data in fake database
        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('speaking')
            .collection('results')
            .doc('result1')
            .set(lessonResult.toJson());

        // Act
        final result = await repository.getSpeakingResult(
          level: 'A1',
          chapter: '1',
          stage: SpeakingStage.stage1,
        );

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value.length,
          1,
        );
        expect((result).value.first.path, '/path/to/speaking');
      });

      test('should return empty list when no documents exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = await repository.getSpeakingResult(
          level: 'A1',
          chapter: '1',
          stage: SpeakingStage.stage1,
        );

        // Assert
        expect(result, isA<Right<AppException, List<LessonResult>>>());
        expect(
          (result as Right<AppException, List<LessonResult>>).value,
          isEmpty,
        );
      });

      test(
        'should return Left with AppException when getting results fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.getSpeakingResult(
            level: 'A1',
            chapter: '1',
            stage: SpeakingStage.stage1,
          );

          // Assert
          expect(result, isA<Left<AppException, List<LessonResult>>>());
          expect(
            (result as Left<AppException, List<LessonResult>>).value.identifier,
            'Failed get speaking result',
          );
        },
      );
    });

    group('calculateSpeakingResult', () {
      test('should calculate speaking aggregate scores successfully', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        final lessonResult1 = LessonResult(
          contentOrder: 1,
          path: '/path/to/content1',
          result: <String, dynamic>{
            'accuracyScore': 80.0,
            'fluencyScore': 75.0,
            'prosodyScore': 85.0,
            'completenessScore': 90.0,
            'pronScore': 82.5,
          },
          speakingStage: SpeakingStage.stage1,
        );

        final lessonResult2 = LessonResult(
          contentOrder: 2,
          path: '/path/to/content2',
          result: <String, dynamic>{
            'accuracyScore': 85.0,
            'fluencyScore': 80.0,
            'prosodyScore': 88.0,
            'completenessScore': 92.0,
            'pronScore': 86.25,
          },
          speakingStage: SpeakingStage.stage1,
        );

        // Set up data in fake database
        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('speaking')
            .collection('results')
            .doc('result1')
            .set(lessonResult1.toJson());

        await fakeDb
            .collection('user-data')
            .doc('u1')
            .collection('lessons')
            .doc('A1')
            .collection('chapters')
            .doc('1')
            .collection('sections')
            .doc('speaking')
            .collection('results')
            .doc('result2')
            .set(lessonResult2.toJson());

        // Act
        final result = await repository.calculateSpeakingResult(
          level: 'A1',
          chapter: '1',
          stage: SpeakingStage.stage1,
        );

        // Assert
        expect(result, isA<Right<AppException, SpeakingAgregateScore>>());
        final aggregateScore =
            (result as Right<AppException, SpeakingAgregateScore>).value;
        expect(aggregateScore.dataCount, 2);
        expect(aggregateScore.accuracyScore, 165.0); // 80.0 + 85.0
        expect(aggregateScore.fluencyScore, 155.0); // 75.0 + 80.0
        expect(aggregateScore.prosodyScore, 173.0); // 85.0 + 88.0
        expect(aggregateScore.completenessScore, 182.0); // 90.0 + 92.0
        expect(aggregateScore.pronScore, 168.75); // 82.5 + 86.25
      });

      test('should return zero scores when no results exist', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = await repository.calculateSpeakingResult(
          level: 'A1',
          chapter: '1',
          stage: SpeakingStage.stage1,
        );

        // Assert
        expect(result, isA<Right<AppException, SpeakingAgregateScore>>());
        final aggregateScore =
            (result as Right<AppException, SpeakingAgregateScore>).value;
        expect(aggregateScore.dataCount, 0);
        expect(aggregateScore.accuracyScore, 0.0);
        expect(aggregateScore.fluencyScore, 0.0);
        expect(aggregateScore.prosodyScore, 0.0);
        expect(aggregateScore.completenessScore, 0.0);
        expect(aggregateScore.pronScore, 0.0);
      });

      test(
        'should return Left with AppException when calculation fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.calculateSpeakingResult(
            level: 'A1',
            chapter: '1',
            stage: SpeakingStage.stage1,
          );

          // Assert
          expect(result, isA<Left<AppException, SpeakingAgregateScore>>());
          expect(
            (result as Left<AppException, SpeakingAgregateScore>)
                .value
                .identifier,
            'Failed get speaking result',
          );
        },
      );
    });

    group('setSectionCompleted', () {
      test('should set section as completed successfully', () async {
        // Arrange
        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn('u1');
        when(() => mockAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = await repository.setSectionCompleted(
          level: 'A1',
          chapter: '1',
          section: SectionType.pronunciation,
        );

        // Assert
        expect(result, isA<Right<AppException, dynamic>>());
        final doc =
            await fakeDb
                .collection('user-data')
                .doc('u1')
                .collection('lessons')
                .doc('A1')
                .collection('chapters')
                .doc('1')
                .collection('sections')
                .doc('pronunciation')
                .get();
        expect(doc.data()!['isComplete'], true);
      });

      test(
        'should return Left with AppException when setting section fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.setSectionCompleted(
            level: 'A1',
            chapter: '1',
            section: SectionType.pronunciation,
          );

          // Assert
          expect(result, isA<Left<AppException, dynamic>>());
          expect(
            (result as Left<AppException, dynamic>).value.identifier,
            'Failed save section complete',
          );
        },
      );
    });

    group('isCertificateReady', () {
      test(
        'should return certificate status when document exists and is complete',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          // Set up certificate data
          await fakeDb
              .collection('user-data')
              .doc('u1')
              .collection('lessons')
              .doc('A1')
              .set({'isComplete': true, 'certificateId': 'cert123'});

          // Act
          final result = await repository.isCertificateReady(level: Level.a1);

          // Assert
          expect(result, isA<Right<AppException, CertificateStatus>>());
          final status =
              (result as Right<AppException, CertificateStatus>).value;
          expect(status.isComplete, true);
          expect(status.certificateId, 'cert123');
        },
      );

      test(
        'should return incomplete status when document does not exist',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          // Act
          final result = await repository.isCertificateReady(level: Level.a1);

          // Assert
          expect(result, isA<Right<AppException, CertificateStatus>>());
          final status =
              (result as Right<AppException, CertificateStatus>).value;
          expect(status.isComplete, false);
          expect(status.certificateId, null);
        },
      );

      test(
        'should return Left with AppException when checking certificate fails',
        () async {
          // Arrange
          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn('u1');
          when(() => mockAuth.currentUser).thenReturn(mockUser);

          // Mock a failure by making the query throw
          when(
            () => mockAuth.currentUser,
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await repository.isCertificateReady(level: Level.a1);

          // Assert
          expect(result, isA<Left<AppException, CertificateStatus>>());
          expect(
            (result as Left<AppException, CertificateStatus>).value.identifier,
            'Failed check level complete',
          );
        },
      );
    });
  });
}

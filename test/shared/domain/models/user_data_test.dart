import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import '../../../helpers/test_data_builders.dart';

void main() {
  final testDate = DateTime.now();

  group('UserData Model Tests', () {
    group('Constructor and Properties', () {
      test('should create UserData with required email', () {
        const userData = UserData(email: '<EMAIL>');

        expect(userData.email, equals('<EMAIL>'));
        expect(userData.afterTest, isFalse);
        expect(userData.lastPronunciation, isNull);
        expect(userData.lastConversation, isNull);
        expect(userData.lastListening, isNull);
        expect(userData.lastSpeaking, isNull);
      });

      test('should create UserData with all properties', () {
        final lastCourse = LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'path1',
        );

        final userData = UserData(
          email: '<EMAIL>',
          afterTest: true,
          lastPronunciation: lastCourse,
          lastConversation: lastCourse,
          lastListening: lastCourse,
          lastSpeaking: lastCourse,
        );

        expect(userData.email, equals('<EMAIL>'));
        expect(userData.afterTest, isTrue);
        expect(userData.lastPronunciation, equals(lastCourse));
        expect(userData.lastConversation, equals(lastCourse));
        expect(userData.lastListening, equals(lastCourse));
        expect(userData.lastSpeaking, equals(lastCourse));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final userData =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(true)
                .build();

        final json = userData.toJson();

        expect(json['email'], equals('<EMAIL>'));
        expect(json['is_after_test'], isTrue);
        expect(json['last_pronunciation'], isNull);
        expect(json['last_conversation'], isNull);
        expect(json['last_listening'], isNull);
        expect(json['last_speaking'], isNull);
      });

      test('should serialize with last courses to JSON correctly', () {
        final lastCourse = LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'path1',
        );
        final userData =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withLastPronunciation(lastCourse)
                .withLastConversation(lastCourse)
                .withLastListening(lastCourse)
                .withLastSpeaking(lastCourse)
                .build();

        final json = userData.toJson();

        expect(json['email'], equals('<EMAIL>'));
        expect(json['last_pronunciation'], isNotNull);
        expect(json['last_conversation'], isNotNull);
        expect(json['last_listening'], isNotNull);
        expect(json['last_speaking'], isNotNull);

        // Note: The current JSON serialization returns LastCourse objects directly
        // This is due to how json_serializable handles nested objects
        expect(json['last_pronunciation'], isA<LastCourse>());
        final lastPronunciation = json['last_pronunciation'] as LastCourse;
        expect(lastPronunciation.level, equals('A1'));
        expect(lastPronunciation.chapter, equals(1));
        expect(lastPronunciation.path, equals('path1'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'email': '<EMAIL>',
          'is_after_test': true,
          'last_pronunciation': null,
          'last_conversation': null,
          'last_listening': null,
          'last_speaking': null,
        };

        final userData = UserData.fromJson(json);

        expect(userData.email, equals('<EMAIL>'));
        expect(userData.afterTest, isTrue);
        expect(userData.lastPronunciation, isNull);
        expect(userData.lastConversation, isNull);
        expect(userData.lastListening, isNull);
        expect(userData.lastSpeaking, isNull);
      });

      test('should deserialize with last courses from JSON correctly', () {
        final json = {
          'email': '<EMAIL>',
          'is_after_test': false,
          'last_pronunciation': {
            'accessTime': testDate.toIso8601String(),
            'level': 'B1',
            'chapter': 2,
            'section': 'pronunciation',
            'path': 'path2',
            'speakingStage': 'stage1',
          },
          'last_conversation': {
            'accessTime': testDate.toIso8601String(),
            'level': 'B2',
            'chapter': 3,
            'section': 'conversation',
            'path': 'path3',
            'speakingStage': 'stage1',
          },
          'last_listening': null,
          'last_speaking': null,
        };

        final userData = UserData.fromJson(json);

        expect(userData.email, equals('<EMAIL>'));
        expect(userData.afterTest, isFalse);
        expect(userData.lastPronunciation?.level, equals('B1'));
        expect(userData.lastPronunciation?.chapter, equals(2));
        expect(userData.lastPronunciation?.path, equals('path2'));
        expect(userData.lastConversation?.level, equals('B2'));
        expect(userData.lastConversation?.chapter, equals(3));
        expect(userData.lastConversation?.path, equals('path3'));
        expect(userData.lastListening, isNull);
        expect(userData.lastSpeaking, isNull);
      });

      test('should handle missing optional fields in JSON', () {
        final json = {'email': '<EMAIL>'};

        final userData = UserData.fromJson(json);

        expect(userData.email, equals('<EMAIL>'));
        expect(userData.afterTest, isFalse); // Default value
        expect(userData.lastPronunciation, isNull);
        expect(userData.lastConversation, isNull);
        expect(userData.lastListening, isNull);
        expect(userData.lastSpeaking, isNull);
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final userData1 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(true)
                .build();

        final userData2 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(true)
                .build();

        expect(userData1, equals(userData2));
        expect(userData1.hashCode, equals(userData2.hashCode));
      });

      test('should not be equal when email is different', () {
        final userData1 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .build();

        final userData2 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .build();

        expect(userData1, isNot(equals(userData2)));
        expect(userData1.hashCode, isNot(equals(userData2.hashCode)));
      });

      test('should not be equal when afterTest is different', () {
        final userData1 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(true)
                .build();

        final userData2 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(false)
                .build();

        expect(userData1, isNot(equals(userData2)));
        expect(userData1.hashCode, isNot(equals(userData2.hashCode)));
      });

      test('should not be equal when last courses are different', () {
        final userData1 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withLastPronunciation(
                  LastCourse(
                    accessTime: testDate,
                    level: 'A1',
                    chapter: 1,
                    section: SectionType.pronunciation,
                    path: 'path1',
                  ),
                )
                .build();

        final userData2 =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withLastPronunciation(
                  LastCourse(
                    accessTime: testDate,
                    level: 'B1',
                    chapter: 2,
                    section: SectionType.pronunciation,
                    path: 'path2',
                  ),
                )
                .build();

        expect(userData1, isNot(equals(userData2)));
        expect(userData1.hashCode, isNot(equals(userData2.hashCode)));
      });
    });

    group('CopyWith Method', () {
      test('should create copy with updated email', () {
        final original =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(true)
                .build();

        final updated = original.copyWith(email: '<EMAIL>');

        expect(updated.email, equals('<EMAIL>'));
        expect(updated.afterTest, equals(original.afterTest));
        expect(updated.lastPronunciation, equals(original.lastPronunciation));
        expect(updated.lastConversation, equals(original.lastConversation));
        expect(updated.lastListening, equals(original.lastListening));
        expect(updated.lastSpeaking, equals(original.lastSpeaking));
      });

      test('should create copy with updated afterTest', () {
        final original =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(false)
                .build();

        final updated = original.copyWith(afterTest: true);

        expect(updated.email, equals(original.email));
        expect(updated.afterTest, isTrue);
        expect(updated.lastPronunciation, equals(original.lastPronunciation));
        expect(updated.lastConversation, equals(original.lastConversation));
        expect(updated.lastListening, equals(original.lastListening));
        expect(updated.lastSpeaking, equals(original.lastSpeaking));
      });

      test('should create copy with updated last courses', () {
        final original =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .build();

        final newLastCourse = LastCourse(
          accessTime: testDate,
          level: 'C1',
          chapter: 5,
          section: SectionType.pronunciation,
          path: 'path5',
        );

        final updated = original.copyWith(lastPronunciation: newLastCourse);

        expect(updated.email, equals(original.email));
        expect(updated.afterTest, equals(original.afterTest));
        expect(updated.lastPronunciation, equals(newLastCourse));
        expect(updated.lastConversation, equals(original.lastConversation));
        expect(updated.lastListening, equals(original.lastListening));
        expect(updated.lastSpeaking, equals(original.lastSpeaking));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final userData =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withAfterTest(true)
                .build();

        final stringRepresentation = userData.toString();

        expect(stringRepresentation, contains('UserData'));
        expect(stringRepresentation, contains('<EMAIL>'));
        expect(stringRepresentation, contains('true'));
      });
    });

    group('Edge Cases', () {
      test('should handle empty email', () {
        const userData = UserData(email: '');

        expect(userData.email, equals(''));
        expect(userData.afterTest, isFalse);
      });

      test('should handle very long email', () {
        final longEmail = '${'a' * 100}@${'b' * 100}.com';
        final userData = UserData(email: longEmail);

        expect(userData.email, equals(longEmail));
      });

      test('should serialize and deserialize consistently', () {
        final lastCourse = LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'path1',
        );
        final original =
            TestDataBuilders.userDataBuilder()
                .withEmail('<EMAIL>')
                .withLastPronunciation(lastCourse)
                .withLastConversation(lastCourse)
                .withLastListening(lastCourse)
                .withLastSpeaking(lastCourse)
                .withCompletedTest()
                .build();

        // Create proper JSON structure manually due to serialization issue
        final json = {
          'email': original.email,
          'is_after_test': original.afterTest,
          'last_pronunciation': original.lastPronunciation?.toJson(),
          'last_conversation': original.lastConversation?.toJson(),
          'last_listening': original.lastListening?.toJson(),
          'last_speaking': original.lastSpeaking?.toJson(),
        };

        final deserialized = UserData.fromJson(json);

        expect(deserialized, equals(original));
      });
    });
  });

  group('LastCourse Model Tests', () {
    test('should create LastCourse with all properties', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
      );

      expect(lastCourse.level, equals('A1'));
      expect(lastCourse.chapter, equals(1));
      expect(lastCourse.path, equals('path1'));
    });

    test('should serialize and deserialize correctly', () {
      final original = LastCourse(
        accessTime: testDate,
        level: 'B1',
        chapter: 2,
        section: SectionType.conversation,
        path: 'path2',
      );

      final json = original.toJson();
      final deserialized = LastCourse.fromJson(json);

      expect(deserialized, equals(original));
      expect(json['level'], equals('B1'));
      expect(json['chapter'], equals(2));
      expect(json['path'], equals('path2'));
    });

    test('should be equal when all properties are the same', () {
      final lastCourse1 = LastCourse(
        accessTime: testDate,
        level: 'C1',
        chapter: 3,
        section: SectionType.listening,
        path: 'path3',
      );

      final lastCourse2 = LastCourse(
        accessTime: testDate,
        level: 'C1',
        chapter: 3,
        section: SectionType.listening,
        path: 'path3',
      );

      expect(lastCourse1, equals(lastCourse2));
      expect(lastCourse1.hashCode, equals(lastCourse2.hashCode));
    });

    test(
      'should include partOrder and subpartOrder for A1 pronunciation section',
      () {
        final lastCourse = LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'path1',
          partOrder: 1,
          subpartOrder: 2,
        );

        final json = lastCourse.toJson();

        expect(lastCourse.partOrder, equals(1));
        expect(lastCourse.subpartOrder, equals(2));
        expect(json['partOrder'], equals(1));
        expect(json['subpartOrder'], equals(2));

        final deserialized = LastCourse.fromJson(json);
        expect(deserialized, equals(lastCourse));
      },
    );
  });

  group('LastCourseInfo Model Tests', () {
    test('should create LastCourseInfo with all properties', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
      );
      final data = {'key': 'value'};

      final lastCourseInfo = LastCourseInfo(info: lastCourse, data: data);

      expect(lastCourseInfo.info, equals(lastCourse));
      expect(lastCourseInfo.data, equals(data));
    });

    test('should create LastCourseInfo with null data', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
      );

      final lastCourseInfo = LastCourseInfo(info: lastCourse);

      expect(lastCourseInfo.info, equals(lastCourse));
      expect(lastCourseInfo.data, isNull);
    });

    test('should serialize and deserialize correctly', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'B1',
        chapter: 2,
        section: SectionType.conversation,
        path: 'path2',
      );
      final data = {'score': 85, 'completed': true};
      final original = LastCourseInfo(info: lastCourse, data: data);

      final json = original.toJson();
      // Note: Due to Freezed serialization behavior, we test the structure instead
      expect(json, isA<Map<String, dynamic>>());
      expect(json['info'], isNotNull);
      expect(json['data'], equals(data));
    });

    test('should be equal when all properties are the same', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'C1',
        chapter: 3,
        section: SectionType.listening,
        path: 'path3',
      );
      final data = {'progress': 0.75};

      final info1 = LastCourseInfo(info: lastCourse, data: data);
      final info2 = LastCourseInfo(info: lastCourse, data: data);

      expect(info1, equals(info2));
      expect(info1.hashCode, equals(info2.hashCode));
    });

    test('should not be equal when info is different', () {
      final lastCourse1 = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
      );
      final lastCourse2 = LastCourse(
        accessTime: testDate,
        level: 'B1',
        chapter: 2,
        section: SectionType.pronunciation,
        path: 'path2',
      );

      final info1 = LastCourseInfo(info: lastCourse1);
      final info2 = LastCourseInfo(info: lastCourse2);

      expect(info1, isNot(equals(info2)));
    });

    test('should not be equal when data is different', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
      );

      final info1 = LastCourseInfo(info: lastCourse, data: {'score': 80});
      final info2 = LastCourseInfo(info: lastCourse, data: {'score': 90});

      expect(info1, isNot(equals(info2)));
    });
  });

  group('LessonResult Model Tests', () {
    test('should create LessonResult with all properties', () {
      final result = LessonResult(
        partOrder: 1,
        subpartOrder: 2,
        speakingStage: SpeakingStage.stage1,
        contentOrder: 5,
        path: 'lesson/path',
        result: {'accuracy': 85.0, 'fluency': 80.0},
      );

      expect(result.partOrder, equals(1));
      expect(result.subpartOrder, equals(2));
      expect(result.speakingStage, equals(SpeakingStage.stage1));
      expect(result.contentOrder, equals(5));
      expect(result.path, equals('lesson/path'));
      expect(result.result, equals({'accuracy': 85.0, 'fluency': 80.0}));
    });

    test('should create LessonResult with null optional properties', () {
      final result = LessonResult(
        contentOrder: 3,
        path: 'lesson/path2',
        result: {'completed': true},
      );

      expect(result.partOrder, isNull);
      expect(result.subpartOrder, isNull);
      expect(result.speakingStage, isNull);
      expect(result.contentOrder, equals(3));
      expect(result.path, equals('lesson/path2'));
      expect(result.result, equals({'completed': true}));
    });

    test('should serialize and deserialize correctly', () {
      final original = LessonResult(
        partOrder: 2,
        subpartOrder: 3,
        speakingStage: SpeakingStage.stage1,
        contentOrder: 10,
        path: 'lesson/path3',
        result: {'score': 92.5, 'time': 45},
      );

      final json = original.toJson();
      final deserialized = LessonResult.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.partOrder, equals(2));
      expect(deserialized.speakingStage, equals(SpeakingStage.stage1));
    });

    test('should be equal when all properties are the same', () {
      final result1 = LessonResult(
        partOrder: 1,
        contentOrder: 5,
        path: 'path1',
        result: {'score': 85},
      );
      final result2 = LessonResult(
        partOrder: 1,
        contentOrder: 5,
        path: 'path1',
        result: {'score': 85},
      );

      expect(result1, equals(result2));
      expect(result1.hashCode, equals(result2.hashCode));
    });

    test('should not be equal when properties are different', () {
      final result1 = LessonResult(
        contentOrder: 5,
        path: 'path1',
        result: {'score': 85},
      );
      final result2 = LessonResult(
        contentOrder: 6,
        path: 'path1',
        result: {'score': 85},
      );

      expect(result1, isNot(equals(result2)));
    });
  });

  group('PronunciationScore Model Tests', () {
    test('should create PronunciationScore with all properties', () {
      final score = PronunciationScore(
        accuracyScore: 85.5,
        fluencyScore: 80.0,
        prosodyScore: 75.5,
        completenessScore: 90.0,
        pronScore: 82.75,
      );

      expect(score.accuracyScore, equals(85.5));
      expect(score.fluencyScore, equals(80.0));
      expect(score.prosodyScore, equals(75.5));
      expect(score.completenessScore, equals(90.0));
      expect(score.pronScore, equals(82.75));
    });

    test('should create PronunciationScore with default values', () {
      const score = PronunciationScore();

      expect(score.accuracyScore, equals(0.0));
      expect(score.fluencyScore, equals(0.0));
      expect(score.prosodyScore, equals(0.0));
      expect(score.completenessScore, equals(0.0));
      expect(score.pronScore, equals(0.0));
    });

    test('should serialize and deserialize correctly', () {
      final original = PronunciationScore(
        accuracyScore: 88.5,
        fluencyScore: 85.0,
        prosodyScore: 82.5,
        completenessScore: 95.0,
        pronScore: 87.75,
      );

      final json = original.toJson();
      final deserialized = PronunciationScore.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.accuracyScore, equals(88.5));
      expect(deserialized.pronScore, equals(87.75));
    });

    test('should be equal when all properties are the same', () {
      final score1 = PronunciationScore(
        accuracyScore: 90.0,
        fluencyScore: 85.0,
        prosodyScore: 80.0,
        completenessScore: 95.0,
        pronScore: 87.5,
      );
      final score2 = PronunciationScore(
        accuracyScore: 90.0,
        fluencyScore: 85.0,
        prosodyScore: 80.0,
        completenessScore: 95.0,
        pronScore: 87.5,
      );

      expect(score1, equals(score2));
      expect(score1.hashCode, equals(score2.hashCode));
    });

    test('should not be equal when properties are different', () {
      final score1 = PronunciationScore(accuracyScore: 90.0);
      final score2 = PronunciationScore(accuracyScore: 85.0);

      expect(score1, isNot(equals(score2)));
    });
  });

  group('PronunciationScoreParams Model Tests', () {
    test('should create PronunciationScoreParams with all properties', () {
      final params = PronunciationScoreParams(
        level: 'B1',
        chapter: '3',
        partOrder: 2,
        subpartOrder: 1,
      );

      expect(params.level, equals('B1'));
      expect(params.chapter, equals('3'));
      expect(params.partOrder, equals(2));
      expect(params.subpartOrder, equals(1));
    });

    test(
      'should create PronunciationScoreParams with null optional properties',
      () {
        final params = PronunciationScoreParams(level: 'A1', chapter: '1');

        expect(params.level, equals('A1'));
        expect(params.chapter, equals('1'));
        expect(params.partOrder, isNull);
        expect(params.subpartOrder, isNull);
      },
    );

    test('should serialize and deserialize correctly', () {
      final original = PronunciationScoreParams(
        level: 'C1',
        chapter: '5',
        partOrder: 3,
        subpartOrder: 2,
      );

      final json = original.toJson();
      final deserialized = PronunciationScoreParams.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.level, equals('C1'));
      expect(deserialized.partOrder, equals(3));
    });

    test('should be equal when all properties are the same', () {
      final params1 = PronunciationScoreParams(
        level: 'B2',
        chapter: '4',
        partOrder: 1,
      );
      final params2 = PronunciationScoreParams(
        level: 'B2',
        chapter: '4',
        partOrder: 1,
      );

      expect(params1, equals(params2));
      expect(params1.hashCode, equals(params2.hashCode));
    });

    test('should not be equal when properties are different', () {
      final params1 = PronunciationScoreParams(level: 'A1', chapter: '1');
      final params2 = PronunciationScoreParams(level: 'A2', chapter: '1');

      expect(params1, isNot(equals(params2)));
    });
  });

  group('PronunciationAgregateScore Model Tests', () {
    test('should create PronunciationAgregateScore with all properties', () {
      final score = PronunciationAgregateScore(
        accuracyScore: 85.5,
        fluencyScore: 80.0,
        prosodyScore: 75.5,
        completenessScore: 90.0,
        pronScore: 82.75,
        dataCount: 10,
      );

      expect(score.accuracyScore, equals(85.5));
      expect(score.dataCount, equals(10));
    });

    test('should create PronunciationAgregateScore with default values', () {
      const score = PronunciationAgregateScore();

      expect(score.accuracyScore, equals(0.0));
      expect(score.dataCount, equals(0));
    });

    test('should serialize and deserialize correctly', () {
      final original = PronunciationAgregateScore(
        accuracyScore: 88.5,
        fluencyScore: 85.0,
        prosodyScore: 82.5,
        completenessScore: 95.0,
        pronScore: 87.75,
        dataCount: 15,
      );

      final json = original.toJson();
      final deserialized = PronunciationAgregateScore.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.dataCount, equals(15));
    });

    test('should be equal when all properties are the same', () {
      final score1 = PronunciationAgregateScore(
        accuracyScore: 90.0,
        dataCount: 5,
      );
      final score2 = PronunciationAgregateScore(
        accuracyScore: 90.0,
        dataCount: 5,
      );

      expect(score1, equals(score2));
      expect(score1.hashCode, equals(score2.hashCode));
    });
  });

  group('SpeakingAgregateScore Model Tests', () {
    test('should create SpeakingAgregateScore with all properties', () {
      final score = SpeakingAgregateScore(
        accuracyScore: 85.5,
        fluencyScore: 80.0,
        prosodyScore: 75.5,
        completenessScore: 90.0,
        pronScore: 82.75,
        dataCount: 8,
      );

      expect(score.accuracyScore, equals(85.5));
      expect(score.dataCount, equals(8));
    });

    test('should create SpeakingAgregateScore with default values', () {
      const score = SpeakingAgregateScore();

      expect(score.accuracyScore, equals(0.0));
      expect(score.dataCount, equals(0));
    });

    test('should serialize and deserialize correctly', () {
      final original = SpeakingAgregateScore(
        accuracyScore: 88.5,
        fluencyScore: 85.0,
        prosodyScore: 82.5,
        completenessScore: 95.0,
        pronScore: 87.75,
        dataCount: 12,
      );

      final json = original.toJson();
      final deserialized = SpeakingAgregateScore.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.dataCount, equals(12));
    });

    test('should be equal when all properties are the same', () {
      final score1 = SpeakingAgregateScore(accuracyScore: 90.0, dataCount: 3);
      final score2 = SpeakingAgregateScore(accuracyScore: 90.0, dataCount: 3);

      expect(score1, equals(score2));
      expect(score1.hashCode, equals(score2.hashCode));
    });
  });

  group('CertificateStatus Model Tests', () {
    test('should create CertificateStatus with all properties', () {
      final status = CertificateStatus(
        isComplete: true,
        certificateId: 'cert123',
      );

      expect(status.isComplete, isTrue);
      expect(status.certificateId, equals('cert123'));
    });

    test('should create CertificateStatus with null certificateId', () {
      final status = CertificateStatus(isComplete: false);

      expect(status.isComplete, isFalse);
      expect(status.certificateId, isNull);
    });

    test('should serialize and deserialize correctly', () {
      final original = CertificateStatus(
        isComplete: true,
        certificateId: 'cert456',
      );

      final json = original.toJson();
      final deserialized = CertificateStatus.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.certificateId, equals('cert456'));
    });

    test('should serialize with null certificateId', () {
      final original = CertificateStatus(isComplete: false);

      final json = original.toJson();
      final deserialized = CertificateStatus.fromJson(json);

      expect(deserialized, equals(original));
      expect(deserialized.certificateId, isNull);
    });

    test('should be equal when all properties are the same', () {
      final status1 = CertificateStatus(
        isComplete: true,
        certificateId: 'cert1',
      );
      final status2 = CertificateStatus(
        isComplete: true,
        certificateId: 'cert1',
      );

      expect(status1, equals(status2));
      expect(status1.hashCode, equals(status2.hashCode));
    });

    test('should not be equal when properties are different', () {
      final status1 = CertificateStatus(isComplete: true);
      final status2 = CertificateStatus(isComplete: false);

      expect(status1, isNot(equals(status2)));
    });
  });

  group('SectionType Enum Tests', () {
    test('should have all expected enum values', () {
      expect(SectionType.pronunciation, equals(SectionType.pronunciation));
      expect(SectionType.conversation, equals(SectionType.conversation));
      expect(SectionType.listening, equals(SectionType.listening));
      expect(SectionType.speaking, equals(SectionType.speaking));
    });

    test('should convert enum values to string correctly', () {
      expect(SectionType.pronunciation.name, equals('pronunciation'));
      expect(SectionType.conversation.name, equals('conversation'));
      expect(SectionType.listening.name, equals('listening'));
      expect(SectionType.speaking.name, equals('speaking'));
    });

    test('should be equal to themselves', () {
      expect(SectionType.pronunciation, equals(SectionType.pronunciation));
      expect(SectionType.conversation, equals(SectionType.conversation));
    });

    test('should not be equal to different values', () {
      expect(
        SectionType.pronunciation,
        isNot(equals(SectionType.conversation)),
      );
      expect(SectionType.listening, isNot(equals(SectionType.speaking)));
    });
  });

  group('Enhanced UserData Tests', () {
    test('should handle copyWith with only some properties updated', () {
      final original = UserData(
        email: '<EMAIL>',
        afterTest: true,
        lastPronunciation: LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'path1',
        ),
      );

      final updated = original.copyWith(
        email: '<EMAIL>',
        afterTest: false,
      );

      expect(updated.email, equals('<EMAIL>'));
      expect(updated.afterTest, isFalse);
      expect(updated.lastPronunciation, equals(original.lastPronunciation));
    });

    test('should handle copyWith with null optional properties', () {
      final original = UserData(
        email: '<EMAIL>',
        lastPronunciation: LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: SectionType.pronunciation,
          path: 'path1',
        ),
      );

      final updated = original.copyWith(
        lastPronunciation: null,
        lastConversation: null,
      );

      expect(updated.email, equals('<EMAIL>'));
      expect(updated.lastPronunciation, isNull);
      expect(updated.lastConversation, isNull);
    });

    test('should handle very long email addresses', () {
      final longEmail = 'a' * 200 + '@example.com';
      final userData = UserData(email: longEmail);

      expect(userData.email, equals(longEmail));
      expect(userData.email.length, equals(212));
    });

    test('should handle special characters in email', () {
      const specialEmail = '<EMAIL>';
      final userData = UserData(email: specialEmail);

      expect(userData.email, equals(specialEmail));
    });
  });

  group('Enhanced LastCourse Tests', () {
    test('should handle different SpeakingStage values', () {
      final course = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
        speakingStage: SpeakingStage.stage1,
      );

      expect(course.speakingStage, equals(SpeakingStage.stage1));
    });

    test('should handle null partOrder and subpartOrder', () {
      final course = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 1,
        section: SectionType.pronunciation,
        path: 'path1',
      );

      expect(course.partOrder, isNull);
      expect(course.subpartOrder, isNull);
    });

    test('should handle all SectionType values', () {
      final sections = [
        SectionType.pronunciation,
        SectionType.conversation,
        SectionType.listening,
        SectionType.speaking,
      ];

      for (final section in sections) {
        final course = LastCourse(
          accessTime: testDate,
          level: 'A1',
          chapter: 1,
          section: section,
          path: 'path1',
        );

        expect(course.section, equals(section));
      }
    });

    test('should handle edge case values', () {
      final course = LastCourse(
        accessTime: DateTime(2020, 1, 1),
        level: '',
        chapter: 0,
        section: SectionType.pronunciation,
        path: '',
        partOrder: 0,
        subpartOrder: 0,
      );

      expect(course.level, equals(''));
      expect(course.chapter, equals(0));
      expect(course.path, equals(''));
      expect(course.partOrder, equals(0));
      expect(course.subpartOrder, equals(0));
    });
  });

  group('Error Conditions and Edge Cases', () {
    test('should handle empty JSON objects gracefully', () {
      // This tests the robustness of fromJson methods
      final emptyJson = <String, dynamic>{};

      // These should throw TypeError due to missing required fields
      expect(() => UserData.fromJson(emptyJson), throwsA(isA<TypeError>()));
    });

    test('should handle malformed JSON data', () {
      final malformedJson = {
        'email': null, // Email should not be null
        'is_after_test': 'not_a_boolean',
      };

      expect(() => UserData.fromJson(malformedJson), throwsA(isA<TypeError>()));
    });

    test('should handle negative numbers in numeric fields', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: -1, // Negative chapter
        section: SectionType.pronunciation,
        path: 'path1',
        partOrder: -5, // Negative partOrder
        subpartOrder: -10, // Negative subpartOrder
      );

      expect(lastCourse.chapter, equals(-1));
      expect(lastCourse.partOrder, equals(-5));
      expect(lastCourse.subpartOrder, equals(-10));
    });

    test('should handle extremely large numbers', () {
      final lastCourse = LastCourse(
        accessTime: testDate,
        level: 'A1',
        chapter: 999999,
        section: SectionType.pronunciation,
        path: 'path1',
        partOrder: 2147483647, // Max int value
      );

      expect(lastCourse.chapter, equals(999999));
      expect(lastCourse.partOrder, equals(2147483647));
    });
  });
}
